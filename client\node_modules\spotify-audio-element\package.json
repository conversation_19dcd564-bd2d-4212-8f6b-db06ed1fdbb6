{"name": "spotify-audio-element", "version": "1.0.2", "description": "A custom element for the Spotify player with an API that aims to match the `<audio>` API", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/spotify-audio-element"}, "files": ["spotify-audio-element.d.ts", "dist"], "type": "module", "types": "./dist/spotify-audio-element.d.ts", "main": "./dist/spotify-audio-element.js", "exports": {".": {"types": "./dist/spotify-audio-element.d.ts", "import": "./dist/spotify-audio-element.js", "require": "./dist/cjs/spotify-audio-element.js", "default": "./dist/spotify-audio-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/spotify-audio-element.d.ts"]}}, "scripts": {"lint": "eslint spotify-audio-element.js", "test": "wet test", "serve": "wet serve", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["spotify", "audio", "player", "web component", "custom element"]}