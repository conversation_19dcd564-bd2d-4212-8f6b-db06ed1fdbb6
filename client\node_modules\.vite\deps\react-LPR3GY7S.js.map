{"version": 3, "sources": ["../../hls-video-element/dist/react.js", "../../hls-video-element/dist/hls-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./hls-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"hls-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "import { CustomVideoElement } from \"custom-media-element\";\nimport { MediaTracksMixin } from \"media-tracks\";\nimport Hls from \"hls.js/dist/hls.mjs\";\nconst HlsVideoMixin = (superclass) => {\n  return class HlsVideo extends superclass {\n    static shadowRootOptions = { ...superclass.shadowRootOptions };\n    static getTemplateHTML = (attrs, props = {}) => {\n      const { src, ...rest } = attrs;\n      return `\n        <script type=\"application/json\" id=\"config\">\n          ${JSON.stringify(props.config || {})}\n        </script>\n        ${superclass.getTemplateHTML(rest)}\n      `;\n    };\n    #airplaySourceEl = null;\n    #config = null;\n    constructor() {\n      super();\n      this.#upgradeProperty(\"config\");\n    }\n    get config() {\n      return this.#config;\n    }\n    set config(value) {\n      this.#config = value;\n    }\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      if (attrName !== \"src\") {\n        super.attributeChangedCallback(attrName, oldValue, newValue);\n      }\n      if (attrName === \"src\" && oldValue != newValue) {\n        this.load();\n      }\n    }\n    #destroy() {\n      var _a, _b;\n      (_a = this.#airplaySourceEl) == null ? void 0 : _a.remove();\n      (_b = this.nativeEl) == null ? void 0 : _b.removeEventListener(\n        \"webkitcurrentplaybacktargetiswirelesschanged\",\n        this.#toggleHlsLoad\n      );\n      if (this.api) {\n        this.api.detachMedia();\n        this.api.destroy();\n        this.api = null;\n      }\n    }\n    async load() {\n      var _a, _b;\n      const isFirstLoad = !this.api;\n      this.#destroy();\n      if (!this.src) {\n        return;\n      }\n      if (isFirstLoad && !this.#config) {\n        this.#config = JSON.parse(((_a = this.shadowRoot.getElementById(\"config\")) == null ? void 0 : _a.textContent) || \"{}\");\n      }\n      if (Hls.isSupported()) {\n        this.api = new Hls({\n          // Mimic the media element with an Infinity duration for live streams.\n          liveDurationInfinity: true,\n          // Disable auto quality level/fragment loading.\n          autoStartLoad: false,\n          // Custom configuration for hls.js.\n          ...this.config\n        });\n        await Promise.resolve();\n        this.api.loadSource(this.src);\n        this.api.attachMedia(this.nativeEl);\n        switch (this.nativeEl.preload) {\n          case \"none\": {\n            const loadSourceOnPlay = () => this.api.startLoad();\n            this.nativeEl.addEventListener(\"play\", loadSourceOnPlay, {\n              once: true\n            });\n            this.api.on(Hls.Events.DESTROYING, () => {\n              this.nativeEl.removeEventListener(\"play\", loadSourceOnPlay);\n            });\n            break;\n          }\n          case \"metadata\": {\n            const originalLength = this.api.config.maxBufferLength;\n            const originalSize = this.api.config.maxBufferSize;\n            this.api.config.maxBufferLength = 1;\n            this.api.config.maxBufferSize = 1;\n            const increaseBufferOnPlay = () => {\n              this.api.config.maxBufferLength = originalLength;\n              this.api.config.maxBufferSize = originalSize;\n            };\n            this.nativeEl.addEventListener(\"play\", increaseBufferOnPlay, {\n              once: true\n            });\n            this.api.on(Hls.Events.DESTROYING, () => {\n              this.nativeEl.removeEventListener(\"play\", increaseBufferOnPlay);\n            });\n            this.api.startLoad();\n            break;\n          }\n          default:\n            this.api.startLoad();\n        }\n        if (this.nativeEl.webkitCurrentPlaybackTargetIsWireless) {\n          this.api.stopLoad();\n        }\n        this.nativeEl.addEventListener(\n          \"webkitcurrentplaybacktargetiswirelesschanged\",\n          this.#toggleHlsLoad\n        );\n        this.#airplaySourceEl = document.createElement(\"source\");\n        this.#airplaySourceEl.setAttribute(\"type\", \"application/x-mpegURL\");\n        this.#airplaySourceEl.setAttribute(\"src\", this.src);\n        this.nativeEl.disableRemotePlayback = false;\n        this.nativeEl.append(this.#airplaySourceEl);\n        const levelIdMap = /* @__PURE__ */ new WeakMap();\n        this.api.on(Hls.Events.MANIFEST_PARSED, (event, data) => {\n          if (this.nativeEl.autoplay && this.nativeEl.paused) {\n            this.nativeEl.play().catch((err) => {\n              console.warn(\"Autoplay failed:\", err);\n            });\n          }\n          removeAllMediaTracks();\n          let videoTrack = this.videoTracks.getTrackById(\"main\");\n          if (!videoTrack) {\n            videoTrack = this.addVideoTrack(\"main\");\n            videoTrack.id = \"main\";\n            videoTrack.selected = true;\n          }\n          for (const [id, level] of data.levels.entries()) {\n            const videoRendition = videoTrack.addRendition(\n              level.url[0],\n              level.width,\n              level.height,\n              level.videoCodec,\n              level.bitrate\n            );\n            levelIdMap.set(level, `${id}`);\n            videoRendition.id = `${id}`;\n          }\n          for (let [id, a] of data.audioTracks.entries()) {\n            const kind = a.default ? \"main\" : \"alternative\";\n            const audioTrack = this.addAudioTrack(kind, a.name, a.lang);\n            audioTrack.id = `${id}`;\n            if (a.default) {\n              audioTrack.enabled = true;\n            }\n          }\n        });\n        this.audioTracks.addEventListener(\"change\", () => {\n          var _a2;\n          const audioTrackId = +((_a2 = [...this.audioTracks].find((t) => t.enabled)) == null ? void 0 : _a2.id);\n          const availableIds = this.api.audioTracks.map((t) => t.id);\n          if (audioTrackId != this.api.audioTrack && availableIds.includes(audioTrackId)) {\n            this.api.audioTrack = audioTrackId;\n          }\n        });\n        this.api.on(Hls.Events.LEVELS_UPDATED, (event, data) => {\n          const videoTrack = this.videoTracks[this.videoTracks.selectedIndex ?? 0];\n          if (!videoTrack) return;\n          const levelIds = data.levels.map((l) => levelIdMap.get(l));\n          for (const rendition of this.videoRenditions) {\n            if (rendition.id && !levelIds.includes(rendition.id)) {\n              videoTrack.removeRendition(rendition);\n            }\n          }\n        });\n        const switchRendition = (event) => {\n          const level = event.target.selectedIndex;\n          if (level != this.api.nextLevel) {\n            this.api.nextLevel = level;\n          }\n        };\n        (_b = this.videoRenditions) == null ? void 0 : _b.addEventListener(\"change\", switchRendition);\n        const removeAllMediaTracks = () => {\n          for (const videoTrack of this.videoTracks) {\n            this.removeVideoTrack(videoTrack);\n          }\n          for (const audioTrack of this.audioTracks) {\n            this.removeAudioTrack(audioTrack);\n          }\n        };\n        this.api.once(Hls.Events.DESTROYING, removeAllMediaTracks);\n        return;\n      }\n      await Promise.resolve();\n      if (this.nativeEl.canPlayType(\"application/vnd.apple.mpegurl\")) {\n        this.nativeEl.src = this.src;\n      }\n    }\n    #toggleHlsLoad = () => {\n      var _a, _b, _c;\n      if ((_a = this.nativeEl) == null ? void 0 : _a.webkitCurrentPlaybackTargetIsWireless) {\n        (_b = this.api) == null ? void 0 : _b.stopLoad();\n      } else {\n        (_c = this.api) == null ? void 0 : _c.startLoad();\n      }\n    };\n    // This is a pattern to update property values that are set before\n    // the custom element is upgraded.\n    // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n    #upgradeProperty(prop) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        delete this[prop];\n        this[prop] = value;\n      }\n    }\n  };\n};\nconst HlsVideoElement = HlsVideoMixin(MediaTracksMixin(CustomVideoElement));\nif (globalThis.customElements && !globalThis.customElements.get(\"hls-video\")) {\n  globalThis.customElements.define(\"hls-video\", HlsVideoElement);\n}\nvar hls_video_element_default = HlsVideoElement;\nexport {\n  Hls,\n  HlsVideoElement,\n  HlsVideoMixin,\n  hls_video_element_default as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,mBAAkB;;;ACAlB,IAAM,gBAAgB,CAAC,eAAe;AACpC,SAAO,MAAM,iBAAiB,WAAW;AAAA,IACvC,OAAO,oBAAoB,EAAE,GAAG,WAAW,kBAAkB;AAAA,IAC7D,OAAO,kBAAkB,CAAC,OAAO,QAAQ,CAAC,MAAM;AAC9C,YAAM,EAAE,KAAK,GAAG,KAAK,IAAI;AACzB,aAAO;AAAA;AAAA,YAED,KAAK,UAAU,MAAM,UAAU,CAAC,CAAC,CAAC;AAAA;AAAA,UAEpC,WAAW,gBAAgB,IAAI,CAAC;AAAA;AAAA,IAEtC;AAAA,IACA,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,cAAc;AACZ,YAAM;AACN,WAAK,iBAAiB,QAAQ;AAAA,IAChC;AAAA,IACA,IAAI,SAAS;AACX,aAAO,KAAK;AAAA,IACd;AAAA,IACA,IAAI,OAAO,OAAO;AAChB,WAAK,UAAU;AAAA,IACjB;AAAA,IACA,yBAAyB,UAAU,UAAU,UAAU;AACrD,UAAI,aAAa,OAAO;AACtB,cAAM,yBAAyB,UAAU,UAAU,QAAQ;AAAA,MAC7D;AACA,UAAI,aAAa,SAAS,YAAY,UAAU;AAC9C,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,IACA,WAAW;AACT,UAAI,IAAI;AACR,OAAC,KAAK,KAAK,qBAAqB,OAAO,SAAS,GAAG,OAAO;AAC1D,OAAC,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG;AAAA,QACzC;AAAA,QACA,KAAK;AAAA,MACP;AACA,UAAI,KAAK,KAAK;AACZ,aAAK,IAAI,YAAY;AACrB,aAAK,IAAI,QAAQ;AACjB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,IACA,MAAM,OAAO;AACX,UAAI,IAAI;AACR,YAAM,cAAc,CAAC,KAAK;AAC1B,WAAK,SAAS;AACd,UAAI,CAAC,KAAK,KAAK;AACb;AAAA,MACF;AACA,UAAI,eAAe,CAAC,KAAK,SAAS;AAChC,aAAK,UAAU,KAAK,QAAQ,KAAK,KAAK,WAAW,eAAe,QAAQ,MAAM,OAAO,SAAS,GAAG,gBAAgB,IAAI;AAAA,MACvH;AACA,UAAI,IAAI,YAAY,GAAG;AACrB,aAAK,MAAM,IAAI,IAAI;AAAA;AAAA,UAEjB,sBAAsB;AAAA;AAAA,UAEtB,eAAe;AAAA;AAAA,UAEf,GAAG,KAAK;AAAA,QACV,CAAC;AACD,cAAM,QAAQ,QAAQ;AACtB,aAAK,IAAI,WAAW,KAAK,GAAG;AAC5B,aAAK,IAAI,YAAY,KAAK,QAAQ;AAClC,gBAAQ,KAAK,SAAS,SAAS;AAAA,UAC7B,KAAK,QAAQ;AACX,kBAAM,mBAAmB,MAAM,KAAK,IAAI,UAAU;AAClD,iBAAK,SAAS,iBAAiB,QAAQ,kBAAkB;AAAA,cACvD,MAAM;AAAA,YACR,CAAC;AACD,iBAAK,IAAI,GAAG,IAAI,OAAO,YAAY,MAAM;AACvC,mBAAK,SAAS,oBAAoB,QAAQ,gBAAgB;AAAA,YAC5D,CAAC;AACD;AAAA,UACF;AAAA,UACA,KAAK,YAAY;AACf,kBAAM,iBAAiB,KAAK,IAAI,OAAO;AACvC,kBAAM,eAAe,KAAK,IAAI,OAAO;AACrC,iBAAK,IAAI,OAAO,kBAAkB;AAClC,iBAAK,IAAI,OAAO,gBAAgB;AAChC,kBAAM,uBAAuB,MAAM;AACjC,mBAAK,IAAI,OAAO,kBAAkB;AAClC,mBAAK,IAAI,OAAO,gBAAgB;AAAA,YAClC;AACA,iBAAK,SAAS,iBAAiB,QAAQ,sBAAsB;AAAA,cAC3D,MAAM;AAAA,YACR,CAAC;AACD,iBAAK,IAAI,GAAG,IAAI,OAAO,YAAY,MAAM;AACvC,mBAAK,SAAS,oBAAoB,QAAQ,oBAAoB;AAAA,YAChE,CAAC;AACD,iBAAK,IAAI,UAAU;AACnB;AAAA,UACF;AAAA,UACA;AACE,iBAAK,IAAI,UAAU;AAAA,QACvB;AACA,YAAI,KAAK,SAAS,uCAAuC;AACvD,eAAK,IAAI,SAAS;AAAA,QACpB;AACA,aAAK,SAAS;AAAA,UACZ;AAAA,UACA,KAAK;AAAA,QACP;AACA,aAAK,mBAAmB,SAAS,cAAc,QAAQ;AACvD,aAAK,iBAAiB,aAAa,QAAQ,uBAAuB;AAClE,aAAK,iBAAiB,aAAa,OAAO,KAAK,GAAG;AAClD,aAAK,SAAS,wBAAwB;AACtC,aAAK,SAAS,OAAO,KAAK,gBAAgB;AAC1C,cAAM,aAA6B,oBAAI,QAAQ;AAC/C,aAAK,IAAI,GAAG,IAAI,OAAO,iBAAiB,CAAC,OAAO,SAAS;AACvD,cAAI,KAAK,SAAS,YAAY,KAAK,SAAS,QAAQ;AAClD,iBAAK,SAAS,KAAK,EAAE,MAAM,CAAC,QAAQ;AAClC,sBAAQ,KAAK,oBAAoB,GAAG;AAAA,YACtC,CAAC;AAAA,UACH;AACA,+BAAqB;AACrB,cAAI,aAAa,KAAK,YAAY,aAAa,MAAM;AACrD,cAAI,CAAC,YAAY;AACf,yBAAa,KAAK,cAAc,MAAM;AACtC,uBAAW,KAAK;AAChB,uBAAW,WAAW;AAAA,UACxB;AACA,qBAAW,CAAC,IAAI,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC/C,kBAAM,iBAAiB,WAAW;AAAA,cAChC,MAAM,IAAI,CAAC;AAAA,cACX,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AACA,uBAAW,IAAI,OAAO,GAAG,EAAE,EAAE;AAC7B,2BAAe,KAAK,GAAG,EAAE;AAAA,UAC3B;AACA,mBAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,QAAQ,GAAG;AAC9C,kBAAM,OAAO,EAAE,UAAU,SAAS;AAClC,kBAAM,aAAa,KAAK,cAAc,MAAM,EAAE,MAAM,EAAE,IAAI;AAC1D,uBAAW,KAAK,GAAG,EAAE;AACrB,gBAAI,EAAE,SAAS;AACb,yBAAW,UAAU;AAAA,YACvB;AAAA,UACF;AAAA,QACF,CAAC;AACD,aAAK,YAAY,iBAAiB,UAAU,MAAM;AAChD,cAAI;AACJ,gBAAM,eAAe,GAAG,MAAM,CAAC,GAAG,KAAK,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,OAAO,SAAS,IAAI;AACnG,gBAAM,eAAe,KAAK,IAAI,YAAY,IAAI,CAAC,MAAM,EAAE,EAAE;AACzD,cAAI,gBAAgB,KAAK,IAAI,cAAc,aAAa,SAAS,YAAY,GAAG;AAC9E,iBAAK,IAAI,aAAa;AAAA,UACxB;AAAA,QACF,CAAC;AACD,aAAK,IAAI,GAAG,IAAI,OAAO,gBAAgB,CAAC,OAAO,SAAS;AACtD,gBAAM,aAAa,KAAK,YAAY,KAAK,YAAY,iBAAiB,CAAC;AACvE,cAAI,CAAC,WAAY;AACjB,gBAAM,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC;AACzD,qBAAW,aAAa,KAAK,iBAAiB;AAC5C,gBAAI,UAAU,MAAM,CAAC,SAAS,SAAS,UAAU,EAAE,GAAG;AACpD,yBAAW,gBAAgB,SAAS;AAAA,YACtC;AAAA,UACF;AAAA,QACF,CAAC;AACD,cAAM,kBAAkB,CAAC,UAAU;AACjC,gBAAM,QAAQ,MAAM,OAAO;AAC3B,cAAI,SAAS,KAAK,IAAI,WAAW;AAC/B,iBAAK,IAAI,YAAY;AAAA,UACvB;AAAA,QACF;AACA,SAAC,KAAK,KAAK,oBAAoB,OAAO,SAAS,GAAG,iBAAiB,UAAU,eAAe;AAC5F,cAAM,uBAAuB,MAAM;AACjC,qBAAW,cAAc,KAAK,aAAa;AACzC,iBAAK,iBAAiB,UAAU;AAAA,UAClC;AACA,qBAAW,cAAc,KAAK,aAAa;AACzC,iBAAK,iBAAiB,UAAU;AAAA,UAClC;AAAA,QACF;AACA,aAAK,IAAI,KAAK,IAAI,OAAO,YAAY,oBAAoB;AACzD;AAAA,MACF;AACA,YAAM,QAAQ,QAAQ;AACtB,UAAI,KAAK,SAAS,YAAY,+BAA+B,GAAG;AAC9D,aAAK,SAAS,MAAM,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,iBAAiB,MAAM;AACrB,UAAI,IAAI,IAAI;AACZ,WAAK,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,uCAAuC;AACpF,SAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,SAAS;AAAA,MACjD,OAAO;AACL,SAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,UAAU;AAAA,MAClD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,iBAAiB,MAAM;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,cAAM,QAAQ,KAAK,IAAI;AACvB,eAAO,KAAK,IAAI;AAChB,aAAK,IAAI,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,kBAAkB,cAAc,iBAAiB,kBAAkB,CAAC;AAC1E,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,WAAW,GAAG;AAC5E,aAAW,eAAe,OAAO,aAAa,eAAe;AAC/D;AACA,IAAI,4BAA4B;;;AD9MhC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAA;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["React"]}