{"name": "cloudflare-video-element", "version": "1.3.3", "description": "A custom element for the Cloudflare player with an API that matches the `<video>` API", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/cloudflare-video-element"}, "files": ["cloudflare-video-element.d.ts", "dist"], "type": "module", "types": "./dist/cloudflare-video-element.d.ts", "main": "./dist/cloudflare-video-element.js", "exports": {".": {"types": "./dist/cloudflare-video-element.d.ts", "import": "./dist/cloudflare-video-element.js", "require": "./dist/cjs/cloudflare-video-element.js", "default": "./dist/cloudflare-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/cloudflare-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet run", "serve": "wet serve", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["cloudflare", "video", "player", "web component", "custom element"]}