{"name": "react-player", "version": "3.3.1", "description": "A React component for playing a variety of URLs, including file paths, Mux, YouTube, Vimeo, and Wistia", "author": "<PERSON> (https://github.com/cookpete)", "license": "MIT", "homepage": "https://github.com/cookpete/react-player", "bugs": {"url": "https://github.com/cookpete/react-player/issues"}, "repository": {"type": "git", "url": "git+https://github.com/cookpete/react-player.git"}, "files": ["dist"], "main": "dist/index.js", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist demo coverage", "start": "run-p 'types -- -w' 'build:esm -- --watch=forever' 'build:demo -- --watch=forever --servedir=demo'", "lint": "biome lint src/*", "test": "tester test/*.tsx test/*/*.tsx test/*.js test/*/*.js --outdir=disttest --platform=node --format=esm --bundle --external:sinon --external:zora --target=esnext --sourcemap=inline", "test:coverage": "c8 --src src --exclude 'test/**' --exclude 'node_modules/**' --exclude 'scripts/**' --exclude-after-remap npm test", "test:codecov": "npm run test:coverage && c8 report --reporter json && codecov -f coverage/coverage-final.json", "types": "tsc", "build": "run-s types build:*", "build:esm": "builder \"src/**/*.*s*\" --outdir=dist --format=esm", "prebuild:demo": "rimraf demo && cp -r examples/react/public demo", "build:demo": "builder ./examples/react/src/index.js --format=esm --splitting --bundle --outdir=demo --minify", "preversion": "run-s lint test", "version": "auto-changelog -p && git add CHANGELOG.md", "prepublishOnly": "npm run build", "postpublish": "npm run clean"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18 || ^19", "react": "^17.0.2 || ^18 || ^19", "react-dom": "^17.0.2 || ^18 || ^19"}, "dependencies": {"@mux/mux-player-react": "^3.5.1", "cloudflare-video-element": "^1.3.3", "dash-video-element": "^0.1.6", "hls-video-element": "^1.5.6", "spotify-audio-element": "^1.0.2", "tiktok-video-element": "^0.1.0", "twitch-video-element": "^0.1.2", "vimeo-video-element": "^1.5.3", "wistia-video-element": "^1.3.3", "youtube-video-element": "^1.6.1"}, "devDependencies": {"@biomejs/biome": "1.8.2", "@types/node": "^20.14.6", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-test-renderer": "^18.3.0", "auto-changelog": "^2.0.0", "builder": "file:./scripts/builder", "c8": "^8.0.1", "codecov": "^3.6.5", "cross-env": "^7.0.2", "esbuild": "^0.21.5", "npm-run-all": "^4.1.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-test-renderer": "^18.3.1", "rimraf": "^3.0.2", "screenfull": "^5.0.2", "sinon": "^16.0.0", "tester": "file:./scripts/tester", "typescript": "^5.4.5", "zora": "^5.2.0"}, "auto-changelog": {"breakingPattern": "Breaking changes:"}, "sideEffects": ["./test/**/*"], "keywords": ["react", "media", "player", "video", "audio", "youtube", "vimeo", "wistia", "hls", "dash", "react-component"]}