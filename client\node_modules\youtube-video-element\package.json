{"name": "youtube-video-element", "version": "1.6.1", "description": "Custom element (web component) for the YouTube player.", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/youtube-video-element#readme", "bugs": {"url": "https://github.com/muxinc/youtube-video-element/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/youtube-video-element"}, "files": ["youtube-video-element.d.ts", "dist"], "type": "module", "types": "./dist/youtube-video-element.d.ts", "main": "./dist/youtube-video-element.js", "exports": {".": {"types": "./dist/youtube-video-element.d.ts", "import": "./dist/youtube-video-element.js", "require": "./dist/cjs/youtube-video-element.js", "default": "./dist/youtube-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/youtube-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet run", "serve": "wet serve", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["youtube", "video", "player", "web component", "custom element"]}