{"name": "dash-video-element", "version": "0.1.6", "description": "Custom element for playing video using the DASH format. Uses dash.js.", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/dash-video-element"}, "files": ["dash-video-element.d.ts", "dist"], "type": "module", "types": "./dist/dash-video-element.d.ts", "main": "./dist/dash-video-element.js", "exports": {".": {"types": "./dist/dash-video-element.d.ts", "import": "./dist/dash-video-element.js", "require": "./dist/cjs/dash-video-element.js", "default": "./dist/dash-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/dash-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet run", "serve": "wet serve", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "dependencies": {"custom-media-element": "^1.4.5", "dashjs": "^5.0.3"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["DASH", "video", "player", "web component", "custom element"]}