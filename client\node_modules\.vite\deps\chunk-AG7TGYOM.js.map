{"version": 3, "sources": ["../../custom-media-element/dist/custom-media-element.js"], "sourcesContent": ["const Events = [\n  \"abort\",\n  \"canplay\",\n  \"canplaythrough\",\n  \"durationchange\",\n  \"emptied\",\n  \"encrypted\",\n  \"ended\",\n  \"error\",\n  \"loadeddata\",\n  \"loadedmetadata\",\n  \"loadstart\",\n  \"pause\",\n  \"play\",\n  \"playing\",\n  \"progress\",\n  \"ratechange\",\n  \"seeked\",\n  \"seeking\",\n  \"stalled\",\n  \"suspend\",\n  \"timeupdate\",\n  \"volumechange\",\n  \"waiting\",\n  \"waitingforkey\",\n  \"resize\",\n  \"enterpictureinpicture\",\n  \"leavepictureinpicture\",\n  \"webkitbeginfullscreen\",\n  \"webkitendfullscreen\",\n  \"webkitpresentationmodechanged\"\n];\nconst Attributes = [\n  \"autopictureinpicture\",\n  \"disablepictureinpicture\",\n  \"disableremoteplayback\",\n  \"autoplay\",\n  \"controls\",\n  \"controlslist\",\n  \"crossorigin\",\n  \"loop\",\n  \"muted\",\n  \"playsinline\",\n  \"poster\",\n  \"preload\",\n  \"src\"\n];\nfunction getAudioTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-flex;\n        line-height: 0;\n        flex-direction: column;\n        justify-content: end;\n      }\n\n      audio {\n        width: 100%;\n      }\n    </style>\n    <slot name=\"media\">\n      <audio${serializeAttributes(attrs)}></audio>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction getVideoTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n      }\n\n      video {\n        max-width: 100%;\n        max-height: 100%;\n        min-width: 100%;\n        min-height: 100%;\n        object-fit: var(--media-object-fit, contain);\n        object-position: var(--media-object-position, 50% 50%);\n      }\n\n      video::-webkit-media-text-track-container {\n        transform: var(--media-webkit-text-track-transform);\n        transition: var(--media-webkit-text-track-transition);\n      }\n    </style>\n    <slot name=\"media\">\n      <video${serializeAttributes(attrs)}></video>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction CustomMediaMixin(superclass, { tag, is }) {\n  const nativeElTest = globalThis.document?.createElement?.(tag, { is });\n  const nativeElProps = nativeElTest ? getNativeElProps(nativeElTest) : [];\n  return class CustomMedia extends superclass {\n    static getTemplateHTML = tag.endsWith(\"audio\") ? getAudioTemplateHTML : getVideoTemplateHTML;\n    static shadowRootOptions = { mode: \"open\" };\n    static Events = Events;\n    static #isDefined = false;\n    static get observedAttributes() {\n      CustomMedia.#define();\n      const natAttrs = nativeElTest?.constructor?.observedAttributes ?? [];\n      return [\n        ...natAttrs,\n        ...Attributes\n      ];\n    }\n    static #define() {\n      if (this.#isDefined) return;\n      this.#isDefined = true;\n      const propsToAttrs = new Set(this.observedAttributes);\n      propsToAttrs.delete(\"muted\");\n      for (const prop of nativeElProps) {\n        if (prop in this.prototype) continue;\n        if (typeof nativeElTest[prop] === \"function\") {\n          this.prototype[prop] = function(...args) {\n            this.#init();\n            const fn = () => {\n              if (this.call) return this.call(prop, ...args);\n              const nativeFn = this.nativeEl?.[prop];\n              return nativeFn?.apply(this.nativeEl, args);\n            };\n            return fn();\n          };\n        } else {\n          const config = {\n            get() {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                const val = this.getAttribute(attr);\n                return val === null ? false : val === \"\" ? true : val;\n              }\n              return this.get?.(prop) ?? this.nativeEl?.[prop];\n            }\n          };\n          if (prop !== prop.toUpperCase()) {\n            config.set = function(val) {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                if (val === true || val === false || val == null) {\n                  this.toggleAttribute(attr, Boolean(val));\n                } else {\n                  this.setAttribute(attr, val);\n                }\n                return;\n              }\n              if (this.set) {\n                this.set(prop, val);\n                return;\n              }\n              if (this.nativeEl) {\n                this.nativeEl[prop] = val;\n              }\n            };\n          }\n          Object.defineProperty(this.prototype, prop, config);\n        }\n      }\n    }\n    // Private fields\n    #isInit = false;\n    #nativeEl = null;\n    #childMap = /* @__PURE__ */ new Map();\n    #childObserver;\n    get;\n    set;\n    call;\n    // If the custom element is defined before the custom element's HTML is parsed\n    // no attributes will be available in the constructor (construction process).\n    // Wait until initializing in the attributeChangedCallback or\n    // connectedCallback or accessing any properties.\n    get nativeEl() {\n      this.#init();\n      return this.#nativeEl ?? this.querySelector(\":scope > [slot=media]\") ?? this.querySelector(tag) ?? this.shadowRoot?.querySelector(tag) ?? null;\n    }\n    set nativeEl(val) {\n      this.#nativeEl = val;\n    }\n    get defaultMuted() {\n      return this.hasAttribute(\"muted\");\n    }\n    set defaultMuted(val) {\n      this.toggleAttribute(\"muted\", val);\n    }\n    get src() {\n      return this.getAttribute(\"src\");\n    }\n    set src(val) {\n      this.setAttribute(\"src\", `${val}`);\n    }\n    get preload() {\n      return this.getAttribute(\"preload\") ?? this.nativeEl?.preload;\n    }\n    set preload(val) {\n      this.setAttribute(\"preload\", `${val}`);\n    }\n    #init() {\n      if (this.#isInit) return;\n      this.#isInit = true;\n      this.init();\n    }\n    init() {\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: \"open\" });\n        const attrs = namedNodeMapToObject(this.attributes);\n        if (is) attrs.is = is;\n        if (tag) attrs.part = tag;\n        this.shadowRoot.innerHTML = this.constructor.getTemplateHTML(attrs);\n      }\n      this.nativeEl.muted = this.hasAttribute(\"muted\");\n      for (const prop of nativeElProps) {\n        this.#upgradeProperty(prop);\n      }\n      this.#childObserver = new MutationObserver(this.#syncMediaChildAttribute.bind(this));\n      this.shadowRoot.addEventListener(\"slotchange\", () => this.#syncMediaChildren());\n      this.#syncMediaChildren();\n      for (const type of this.constructor.Events) {\n        this.shadowRoot.addEventListener(type, this, true);\n      }\n    }\n    handleEvent(event) {\n      if (event.target === this.nativeEl) {\n        this.dispatchEvent(new CustomEvent(event.type, { detail: event.detail }));\n      }\n    }\n    #syncMediaChildren() {\n      const removeNativeChildren = new Map(this.#childMap);\n      const defaultSlot = this.shadowRoot?.querySelector(\"slot:not([name])\");\n      const mediaChildren = defaultSlot?.assignedElements({ flatten: true }).filter((el) => [\"track\", \"source\"].includes(el.localName));\n      mediaChildren.forEach((el) => {\n        removeNativeChildren.delete(el);\n        let clone = this.#childMap.get(el);\n        if (!clone) {\n          clone = el.cloneNode();\n          this.#childMap.set(el, clone);\n          this.#childObserver?.observe(el, { attributes: true });\n        }\n        this.nativeEl?.append(clone);\n        this.#enableDefaultTrack(clone);\n      });\n      removeNativeChildren.forEach((clone, el) => {\n        clone.remove();\n        this.#childMap.delete(el);\n      });\n    }\n    #syncMediaChildAttribute(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === \"attributes\") {\n          const { target, attributeName } = mutation;\n          const clone = this.#childMap.get(target);\n          if (clone && attributeName) {\n            clone.setAttribute(attributeName, target.getAttribute(attributeName) ?? \"\");\n            this.#enableDefaultTrack(clone);\n          }\n        }\n      }\n    }\n    #enableDefaultTrack(trackEl) {\n      if (trackEl && trackEl.localName === \"track\" && trackEl.default && (trackEl.kind === \"chapters\" || trackEl.kind === \"metadata\") && trackEl.track.mode === \"disabled\") {\n        trackEl.track.mode = \"hidden\";\n      }\n    }\n    #upgradeProperty(prop) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        delete this[prop];\n        this[prop] = value;\n      }\n    }\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      this.#init();\n      this.#forwardAttribute(attrName, oldValue, newValue);\n    }\n    #forwardAttribute(attrName, _oldValue, newValue) {\n      if ([\"id\", \"class\"].includes(attrName)) return;\n      if (!CustomMedia.observedAttributes.includes(attrName) && this.constructor.observedAttributes.includes(attrName)) {\n        return;\n      }\n      if (newValue === null) {\n        this.nativeEl?.removeAttribute(attrName);\n      } else if (this.nativeEl?.getAttribute(attrName) !== newValue) {\n        this.nativeEl?.setAttribute(attrName, newValue);\n      }\n    }\n    connectedCallback() {\n      this.#init();\n    }\n  };\n}\nfunction getNativeElProps(nativeElTest) {\n  const nativeElProps = [];\n  for (let proto = Object.getPrototypeOf(nativeElTest); proto && proto !== HTMLElement.prototype; proto = Object.getPrototypeOf(proto)) {\n    const props = Object.getOwnPropertyNames(proto);\n    nativeElProps.push(...props);\n  }\n  return nativeElProps;\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    if (!Attributes.includes(key)) continue;\n    const value = attrs[key];\n    if (value === \"\") html += ` ${key}`;\n    else html += ` ${key}=\"${value}\"`;\n  }\n  return html;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  const obj = {};\n  for (const attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst CustomVideoElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"video\"\n});\nconst CustomAudioElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"audio\"\n});\nexport {\n  Attributes,\n  CustomAudioElement,\n  CustomMediaMixin,\n  CustomVideoElement,\n  Events\n};\n"], "mappings": ";AAAA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,aAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,qBAAqB,OAAO;AACnC;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAcU,oBAAoB,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAKxC;AACA,SAAS,qBAAqB,OAAO;AACnC;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAsBU,oBAAoB,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAKxC;AACA,SAAS,iBAAiB,YAAY,EAAE,KAAK,GAAG,GAAG;AACjD,QAAM,eAAe,WAAW,UAAU,gBAAgB,KAAK,EAAE,GAAG,CAAC;AACrE,QAAM,gBAAgB,eAAe,iBAAiB,YAAY,IAAI,CAAC;AACvE,SAAO,MAAM,oBAAoB,WAAW;AAAA,IAC1C,OAAO,kBAAkB,IAAI,SAAS,OAAO,IAAI,uBAAuB;AAAA,IACxE,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,IAC1C,OAAO,SAAS;AAAA,IAChB,OAAO,aAAa;AAAA,IACpB,WAAW,qBAAqB;AAC9B,kBAAY,QAAQ;AACpB,YAAM,WAAW,cAAc,aAAa,sBAAsB,CAAC;AACnE,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,IACA,OAAO,UAAU;AACf,UAAI,KAAK,WAAY;AACrB,WAAK,aAAa;AAClB,YAAM,eAAe,IAAI,IAAI,KAAK,kBAAkB;AACpD,mBAAa,OAAO,OAAO;AAC3B,iBAAW,QAAQ,eAAe;AAChC,YAAI,QAAQ,KAAK,UAAW;AAC5B,YAAI,OAAO,aAAa,IAAI,MAAM,YAAY;AAC5C,eAAK,UAAU,IAAI,IAAI,YAAY,MAAM;AACvC,iBAAK,MAAM;AACX,kBAAM,KAAK,MAAM;AACf,kBAAI,KAAK,KAAM,QAAO,KAAK,KAAK,MAAM,GAAG,IAAI;AAC7C,oBAAM,WAAW,KAAK,WAAW,IAAI;AACrC,qBAAO,UAAU,MAAM,KAAK,UAAU,IAAI;AAAA,YAC5C;AACA,mBAAO,GAAG;AAAA,UACZ;AAAA,QACF,OAAO;AACL,gBAAM,SAAS;AAAA,YACb,MAAM;AACJ,mBAAK,MAAM;AACX,oBAAM,OAAO,KAAK,YAAY;AAC9B,kBAAI,aAAa,IAAI,IAAI,GAAG;AAC1B,sBAAM,MAAM,KAAK,aAAa,IAAI;AAClC,uBAAO,QAAQ,OAAO,QAAQ,QAAQ,KAAK,OAAO;AAAA,cACpD;AACA,qBAAO,KAAK,MAAM,IAAI,KAAK,KAAK,WAAW,IAAI;AAAA,YACjD;AAAA,UACF;AACA,cAAI,SAAS,KAAK,YAAY,GAAG;AAC/B,mBAAO,MAAM,SAAS,KAAK;AACzB,mBAAK,MAAM;AACX,oBAAM,OAAO,KAAK,YAAY;AAC9B,kBAAI,aAAa,IAAI,IAAI,GAAG;AAC1B,oBAAI,QAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAChD,uBAAK,gBAAgB,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACzC,OAAO;AACL,uBAAK,aAAa,MAAM,GAAG;AAAA,gBAC7B;AACA;AAAA,cACF;AACA,kBAAI,KAAK,KAAK;AACZ,qBAAK,IAAI,MAAM,GAAG;AAClB;AAAA,cACF;AACA,kBAAI,KAAK,UAAU;AACjB,qBAAK,SAAS,IAAI,IAAI;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AACA,iBAAO,eAAe,KAAK,WAAW,MAAM,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAA4B,oBAAI,IAAI;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,WAAW;AACb,WAAK,MAAM;AACX,aAAO,KAAK,aAAa,KAAK,cAAc,uBAAuB,KAAK,KAAK,cAAc,GAAG,KAAK,KAAK,YAAY,cAAc,GAAG,KAAK;AAAA,IAC5I;AAAA,IACA,IAAI,SAAS,KAAK;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,IAAI,eAAe;AACjB,aAAO,KAAK,aAAa,OAAO;AAAA,IAClC;AAAA,IACA,IAAI,aAAa,KAAK;AACpB,WAAK,gBAAgB,SAAS,GAAG;AAAA,IACnC;AAAA,IACA,IAAI,MAAM;AACR,aAAO,KAAK,aAAa,KAAK;AAAA,IAChC;AAAA,IACA,IAAI,IAAI,KAAK;AACX,WAAK,aAAa,OAAO,GAAG,GAAG,EAAE;AAAA,IACnC;AAAA,IACA,IAAI,UAAU;AACZ,aAAO,KAAK,aAAa,SAAS,KAAK,KAAK,UAAU;AAAA,IACxD;AAAA,IACA,IAAI,QAAQ,KAAK;AACf,WAAK,aAAa,WAAW,GAAG,GAAG,EAAE;AAAA,IACvC;AAAA,IACA,QAAQ;AACN,UAAI,KAAK,QAAS;AAClB,WAAK,UAAU;AACf,WAAK,KAAK;AAAA,IACZ;AAAA,IACA,OAAO;AACL,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAClC,cAAM,QAAQ,qBAAqB,KAAK,UAAU;AAClD,YAAI,GAAI,OAAM,KAAK;AACnB,YAAI,IAAK,OAAM,OAAO;AACtB,aAAK,WAAW,YAAY,KAAK,YAAY,gBAAgB,KAAK;AAAA,MACpE;AACA,WAAK,SAAS,QAAQ,KAAK,aAAa,OAAO;AAC/C,iBAAW,QAAQ,eAAe;AAChC,aAAK,iBAAiB,IAAI;AAAA,MAC5B;AACA,WAAK,iBAAiB,IAAI,iBAAiB,KAAK,yBAAyB,KAAK,IAAI,CAAC;AACnF,WAAK,WAAW,iBAAiB,cAAc,MAAM,KAAK,mBAAmB,CAAC;AAC9E,WAAK,mBAAmB;AACxB,iBAAW,QAAQ,KAAK,YAAY,QAAQ;AAC1C,aAAK,WAAW,iBAAiB,MAAM,MAAM,IAAI;AAAA,MACnD;AAAA,IACF;AAAA,IACA,YAAY,OAAO;AACjB,UAAI,MAAM,WAAW,KAAK,UAAU;AAClC,aAAK,cAAc,IAAI,YAAY,MAAM,MAAM,EAAE,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,qBAAqB;AACnB,YAAM,uBAAuB,IAAI,IAAI,KAAK,SAAS;AACnD,YAAM,cAAc,KAAK,YAAY,cAAc,kBAAkB;AACrE,YAAM,gBAAgB,aAAa,iBAAiB,EAAE,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,QAAQ,EAAE,SAAS,GAAG,SAAS,CAAC;AAChI,oBAAc,QAAQ,CAAC,OAAO;AAC5B,6BAAqB,OAAO,EAAE;AAC9B,YAAI,QAAQ,KAAK,UAAU,IAAI,EAAE;AACjC,YAAI,CAAC,OAAO;AACV,kBAAQ,GAAG,UAAU;AACrB,eAAK,UAAU,IAAI,IAAI,KAAK;AAC5B,eAAK,gBAAgB,QAAQ,IAAI,EAAE,YAAY,KAAK,CAAC;AAAA,QACvD;AACA,aAAK,UAAU,OAAO,KAAK;AAC3B,aAAK,oBAAoB,KAAK;AAAA,MAChC,CAAC;AACD,2BAAqB,QAAQ,CAAC,OAAO,OAAO;AAC1C,cAAM,OAAO;AACb,aAAK,UAAU,OAAO,EAAE;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,IACA,yBAAyB,WAAW;AAClC,iBAAW,YAAY,WAAW;AAChC,YAAI,SAAS,SAAS,cAAc;AAClC,gBAAM,EAAE,QAAQ,cAAc,IAAI;AAClC,gBAAM,QAAQ,KAAK,UAAU,IAAI,MAAM;AACvC,cAAI,SAAS,eAAe;AAC1B,kBAAM,aAAa,eAAe,OAAO,aAAa,aAAa,KAAK,EAAE;AAC1E,iBAAK,oBAAoB,KAAK;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB,SAAS;AAC3B,UAAI,WAAW,QAAQ,cAAc,WAAW,QAAQ,YAAY,QAAQ,SAAS,cAAc,QAAQ,SAAS,eAAe,QAAQ,MAAM,SAAS,YAAY;AACpK,gBAAQ,MAAM,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,IACA,iBAAiB,MAAM;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,cAAM,QAAQ,KAAK,IAAI;AACvB,eAAO,KAAK,IAAI;AAChB,aAAK,IAAI,IAAI;AAAA,MACf;AAAA,IACF;AAAA,IACA,yBAAyB,UAAU,UAAU,UAAU;AACrD,WAAK,MAAM;AACX,WAAK,kBAAkB,UAAU,UAAU,QAAQ;AAAA,IACrD;AAAA,IACA,kBAAkB,UAAU,WAAW,UAAU;AAC/C,UAAI,CAAC,MAAM,OAAO,EAAE,SAAS,QAAQ,EAAG;AACxC,UAAI,CAAC,YAAY,mBAAmB,SAAS,QAAQ,KAAK,KAAK,YAAY,mBAAmB,SAAS,QAAQ,GAAG;AAChH;AAAA,MACF;AACA,UAAI,aAAa,MAAM;AACrB,aAAK,UAAU,gBAAgB,QAAQ;AAAA,MACzC,WAAW,KAAK,UAAU,aAAa,QAAQ,MAAM,UAAU;AAC7D,aAAK,UAAU,aAAa,UAAU,QAAQ;AAAA,MAChD;AAAA,IACF;AAAA,IACA,oBAAoB;AAClB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,cAAc;AACtC,QAAM,gBAAgB,CAAC;AACvB,WAAS,QAAQ,OAAO,eAAe,YAAY,GAAG,SAAS,UAAU,YAAY,WAAW,QAAQ,OAAO,eAAe,KAAK,GAAG;AACpI,UAAM,QAAQ,OAAO,oBAAoB,KAAK;AAC9C,kBAAc,KAAK,GAAG,KAAK;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,QAAI,CAAC,WAAW,SAAS,GAAG,EAAG;AAC/B,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,GAAG;AAAA,QAC5B,SAAQ,IAAI,GAAG,KAAK,KAAK;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,cAAc;AAC1C,QAAM,MAAM,CAAC;AACb,aAAW,QAAQ,cAAc;AAC/B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAM,qBAAqB,iBAAiB,WAAW,eAAe,MAAM;AAC5E,GAAG;AAAA,EACD,KAAK;AACP,CAAC;AACD,IAAM,qBAAqB,iBAAiB,WAAW,eAAe,MAAM;AAC5E,GAAG;AAAA,EACD,KAAK;AACP,CAAC;", "names": []}