import React, { useState } from 'react'
import { dummyTrailers } from '../assets/assets'
import ReactPlayer from 'react-player'
import BlurCircle from './BlurCircle'
import { PlayCircleIcon } from 'lucide-react'



const TrailerSection = () => {
    const [currentTrailer, setCurrentTrailer] = useState(dummyTrailers[0])
  return (
    <div className='px-6 md:px-16 lg:px-26 xl:px-44 py-20 overflow-hidden'>
        <p className='text-gray-300 font-medium text-lg max-w-[960px] mx-auto'>
            Trailer</p>

            <div className='relative mt-6'>
                <BlurCircle top='-100px' right='-100px' />
            <ReactPlayer url={currentTrailer.videoUrl} controls={true} 
            className='mx-auto max-w-full' width="960px" height="540px" />
            </div>

            <div className='grid grid-cols-2  gap-4 md:gap-8 mt-8 max-w-3xl mx-auto'>
                {dummyTrailers.map((trailer)=>(
                    <div key={trailer.image} alt='trailer' className='relative group-hover:not-hover::opacity-50 hover:-translate-y-1 duration-300
                    transition max-md:h-60 md:max-h-60 cursor-pointer' onClick={() => setCurrentTrailer(trailer)}>
                        <img src="{trailer.image}" alt="trailer" className='rounded-lg cursor-pointer w-full h-full object-cover brightness-75' />
                        <PlayCircleIcon  className=' absolute top-1/2 left-1/2 md:w-8 md:h-12 transform -translate-x-1/2 -translate-y-1/2 text-white w-5 h-10'/>
                        </div>
                ))}

            </div>
      
    </div>
  )
}

export default TrailerSection
