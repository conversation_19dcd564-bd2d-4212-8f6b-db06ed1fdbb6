{"version": 3, "sources": ["../../twitch-video-element/dist/react.js", "../../twitch-video-element/dist/twitch-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./twitch-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"twitch-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "const EMBED_BASE = \"https://player.twitch.tv\";\nconst MATCH_VIDEO = /(?:www\\.|go\\.)?twitch\\.tv\\/(?:videos?\\/|\\?video=)(\\d+)($|\\?)/;\nconst MATCH_CHANNEL = /(?:www\\.|go\\.)?twitch\\.tv\\/([a-zA-Z0-9_]+)($|\\?)/;\nconst PlaybackState = {\n  IDLE: \"Idle\",\n  READY: \"Ready\",\n  BUFFERING: \"Buffering\",\n  PLAYING: \"Playing\",\n  ENDED: \"Ended\"\n};\nconst PlayerCommands = {\n  DISABLE_CAPTIONS: 0,\n  ENABLE_CAPTIONS: 1,\n  PAUSE: 2,\n  PLAY: 3,\n  SEEK: 4,\n  SET_CHANNEL: 5,\n  SET_CHANNEL_ID: 6,\n  SET_COLLECTION: 7,\n  SET_QUALITY: 8,\n  SET_VIDEO: 9,\n  SET_MUTED: 10,\n  SET_VOLUME: 11\n};\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; picture-in-picture;\",\n    sandbox: \"allow-modals allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox\",\n    scrolling: \"no\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n      }\n      :host(:not([controls])) {\n        pointer-events: none;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  var _a;\n  if (!attrs.src) return;\n  const videoMatch = attrs.src.match(MATCH_VIDEO);\n  const channelMatch = attrs.src.match(MATCH_CHANNEL);\n  const params = {\n    parent: (_a = globalThis.location) == null ? void 0 : _a.hostname,\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : false,\n    autoplay: attrs.autoplay === \"\" ? null : false,\n    muted: attrs.muted,\n    preload: attrs.preload,\n    ...props.config\n  };\n  if (videoMatch) {\n    const videoId = videoMatch[1];\n    return `${EMBED_BASE}/?video=v${videoId}&${serialize(params)}`;\n  } else if (channelMatch) {\n    const channel = channelMatch[1];\n    return `${EMBED_BASE}/?channel=${channel}&${serialize(params)}`;\n  }\n  return \"\";\n}\nclass TwitchVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\"autoplay\", \"controls\", \"loop\", \"muted\", \"playsinline\", \"preload\", \"src\"];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #iframe;\n  #playerState = {};\n  #currentTime = 0;\n  #muted = false;\n  #volume = 1;\n  #paused = !this.autoplay;\n  #seeking = false;\n  #readyState = 0;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#readyState = 0;\n    this.dispatchEvent(new Event(\"emptied\"));\n    if (!this.src) {\n      this.shadowRoot.innerHTML = \"\";\n      globalThis.removeEventListener(\"message\", this.#onMessage);\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    const attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.#iframe = iframe;\n    globalThis.addEventListener(\"message\", this.#onMessage);\n  }\n  attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\":\n      case \"controls\": {\n        this.load();\n        break;\n      }\n    }\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(value) {\n    this.setAttribute(\"src\", value);\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get buffered() {\n    var _a, _b;\n    return createTimeRanges(0, ((_b = (_a = this.#playerState.stats) == null ? void 0 : _a.videoStats) == null ? void 0 : _b.bufferSize) ?? 0);\n  }\n  get paused() {\n    if (!this.#playerState.playback) return this.#paused;\n    return this.#playerState.playback === PlaybackState.IDLE;\n  }\n  get ended() {\n    if (!this.#playerState.playback) return false;\n    return this.#playerState.playback === PlaybackState.ENDED;\n  }\n  get duration() {\n    return this.#playerState.duration ?? NaN;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    if (!this.#playerState.currentTime) return this.#currentTime;\n    return this.#playerState.currentTime;\n  }\n  set currentTime(val) {\n    this.#currentTime = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SEEK, val);\n    });\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    this.#muted = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SET_MUTED, val);\n    });\n  }\n  get volume() {\n    return this.#volume;\n  }\n  set volume(val) {\n    this.#volume = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SET_VOLUME, val);\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  play() {\n    this.#paused = false;\n    this.#sendCommand(PlayerCommands.PLAY);\n  }\n  pause() {\n    this.#paused = true;\n    this.#sendCommand(PlayerCommands.PAUSE);\n  }\n  #onMessage = async (event) => {\n    var _a, _b, _c, _d;\n    if (!this.#iframe.contentWindow) return;\n    const { data, source } = event;\n    const isFromEmbedWindow = source === this.#iframe.contentWindow;\n    if (!isFromEmbedWindow) return;\n    if (data.namespace === \"twitch-embed\") {\n      await new Promise((resolve) => setTimeout(resolve, 10));\n      if (data.eventName === \"ready\") {\n        this.dispatchEvent(new Event(\"loadcomplete\"));\n        this.loadComplete.resolve();\n        this.#readyState = 1;\n        this.dispatchEvent(new Event(\"loadedmetadata\"));\n      } else if (data.eventName === \"seek\") {\n        this.#seeking = true;\n        this.dispatchEvent(new Event(\"seeking\"));\n      } else if (data.eventName === \"playing\") {\n        if (this.#seeking) {\n          this.#seeking = false;\n          this.dispatchEvent(new Event(\"seeked\"));\n        }\n        this.#readyState = 3;\n        this.dispatchEvent(new Event(\"playing\"));\n      } else {\n        this.dispatchEvent(new Event(data.eventName));\n      }\n    } else if (data.namespace === \"twitch-embed-player-proxy\" && data.eventName === \"UPDATE_STATE\") {\n      const oldDuration = this.#playerState.duration;\n      const oldCurrentTime = this.#playerState.currentTime;\n      const oldVolume = this.#playerState.volume;\n      const oldMuted = this.#playerState.muted;\n      const oldBuffered = (_b = (_a = this.#playerState.stats) == null ? void 0 : _a.videoStats) == null ? void 0 : _b.bufferSize;\n      this.#playerState = { ...this.#playerState, ...data.params };\n      if (oldDuration !== this.#playerState.duration) {\n        this.dispatchEvent(new Event(\"durationchange\"));\n      }\n      if (oldCurrentTime !== this.#playerState.currentTime) {\n        this.dispatchEvent(new Event(\"timeupdate\"));\n      }\n      if (oldVolume !== this.#playerState.volume || oldMuted !== this.#playerState.muted) {\n        this.dispatchEvent(new Event(\"volumechange\"));\n      }\n      if (oldBuffered !== ((_d = (_c = this.#playerState.stats) == null ? void 0 : _c.videoStats) == null ? void 0 : _d.bufferSize)) {\n        this.dispatchEvent(new Event(\"progress\"));\n      }\n    }\n  };\n  #sendCommand(command, params) {\n    if (!this.#iframe.contentWindow) return;\n    const message = {\n      eventName: command,\n      params,\n      namespace: \"twitch-embed-player-proxy\"\n    };\n    this.#iframe.contentWindow.postMessage(message, EMBED_BASE);\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(filterParams(props)));\n}\nfunction filterParams(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = true;\n    else if (val === false) p[key] = false;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"twitch-video\")) {\n  globalThis.customElements.define(\"twitch-video\", TwitchVideoElement);\n}\nvar twitch_video_element_default = TwitchVideoElement;\nexport {\n  twitch_video_element_default as default\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACHlB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AACT;AACA,IAAM,iBAAiB;AAAA,EACrB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AACd;AACA,SAAS,gBAAgB,OAAO,QAAQ,CAAC,GAAG;AAC1C,QAAM,cAAc;AAAA,IAClB,KAAK,mBAAmB,OAAO,KAAK;AAAA,IACpC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACA,MAAI,MAAM,QAAQ;AAChB,gBAAY,aAAa,IAAI,KAAK,UAAU,MAAM,MAAM;AAAA,EAC1D;AACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAmBS,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAG7C;AACA,SAAS,mBAAmB,OAAO,OAAO;AACxC,MAAI;AACJ,MAAI,CAAC,MAAM,IAAK;AAChB,QAAM,aAAa,MAAM,IAAI,MAAM,WAAW;AAC9C,QAAM,eAAe,MAAM,IAAI,MAAM,aAAa;AAClD,QAAM,SAAS;AAAA,IACb,SAAS,KAAK,WAAW,aAAa,OAAO,SAAS,GAAG;AAAA;AAAA,IAEzD,UAAU,MAAM,aAAa,KAAK,OAAO;AAAA,IACzC,UAAU,MAAM,aAAa,KAAK,OAAO;AAAA,IACzC,OAAO,MAAM;AAAA,IACb,SAAS,MAAM;AAAA,IACf,GAAG,MAAM;AAAA,EACX;AACA,MAAI,YAAY;AACd,UAAM,UAAU,WAAW,CAAC;AAC5B,WAAO,GAAG,UAAU,YAAY,OAAO,IAAI,UAAU,MAAM,CAAC;AAAA,EAC9D,WAAW,cAAc;AACvB,UAAM,UAAU,aAAa,CAAC;AAC9B,WAAO,GAAG,UAAU,aAAa,OAAO,IAAI,UAAU,MAAM,CAAC;AAAA,EAC/D;AACA,SAAO;AACT;AACA,IAAM,qBAAN,eAAkC,WAAW,eAAe,MAAM;AAClE,GAAG;AAAA,EACD,OAAO,kBAAkB;AAAA,EACzB,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,EAC1C,OAAO,qBAAqB,CAAC,YAAY,YAAY,QAAQ,SAAS,eAAe,WAAW,KAAK;AAAA,EACrG,eAAe,IAAI,cAAc;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe,CAAC;AAAA,EAChB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU,CAAC,KAAK;AAAA,EAChB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,eAAgB;AACzB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,IACpC;AACA,UAAM,cAAc,CAAC,KAAK;AAC1B,QAAI,KAAK,YAAY;AACnB,WAAK,eAAe,IAAI,cAAc;AAAA,IACxC;AACA,SAAK,aAAa;AAClB,WAAO,KAAK,iBAAiB,QAAQ,QAAQ;AAC7C,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC,QAAI,CAAC,KAAK,KAAK;AACb,WAAK,WAAW,YAAY;AAC5B,iBAAW,oBAAoB,WAAW,KAAK,UAAU;AACzD;AAAA,IACF;AACA,SAAK,cAAc,IAAI,MAAM,WAAW,CAAC;AACzC,QAAI,SAAS,KAAK,WAAW,cAAc,QAAQ;AACnD,UAAM,QAAQ,qBAAqB,KAAK,UAAU;AAClD,QAAI,eAAe,QAAQ;AACzB,WAAK,UAAU,KAAK,MAAM,OAAO,aAAa,aAAa,KAAK,IAAI;AAAA,IACtE;AACA,QAAI,EAAE,UAAU,OAAO,SAAS,OAAO,QAAQ,OAAO,QAAQ,mBAAmB,OAAO,IAAI,GAAG;AAC7F,WAAK,WAAW,YAAY,gBAAgB,OAAO,IAAI;AACvD,eAAS,KAAK,WAAW,cAAc,QAAQ;AAAA,IACjD;AACA,SAAK,UAAU;AACf,eAAW,iBAAiB,WAAW,KAAK,UAAU;AAAA,EACxD;AAAA,EACA,yBAAyB,UAAU,UAAU,UAAU;AACrD,QAAI,aAAa,SAAU;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK;AAAA,MACL,KAAK,YAAY;AACf,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,IAAI,OAAO;AACb,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,QAAI,IAAI;AACR,WAAO,iBAAiB,KAAK,MAAM,KAAK,KAAK,aAAa,UAAU,OAAO,SAAS,GAAG,eAAe,OAAO,SAAS,GAAG,eAAe,CAAC;AAAA,EAC3I;AAAA,EACA,IAAI,SAAS;AACX,QAAI,CAAC,KAAK,aAAa,SAAU,QAAO,KAAK;AAC7C,WAAO,KAAK,aAAa,aAAa,cAAc;AAAA,EACtD;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,CAAC,KAAK,aAAa,SAAU,QAAO;AACxC,WAAO,KAAK,aAAa,aAAa,cAAc;AAAA,EACtD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,YAAY;AAAA,EACvC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,CAAC,KAAK,aAAa,YAAa,QAAO,KAAK;AAChD,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK,MAAM;AAC3B,WAAK,aAAa,eAAe,MAAM,GAAG;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,SAAK,gBAAgB,SAAS,QAAQ,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa,MAAM;AAAA,EACjC;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,SAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,MAAM;AAC3B,WAAK,aAAa,eAAe,WAAW,GAAG;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,KAAK;AACd,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,MAAM;AAC3B,WAAK,aAAa,eAAe,YAAY,GAAG;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AAAA,EAClD;AAAA,EACA,OAAO;AACL,SAAK,UAAU;AACf,SAAK,aAAa,eAAe,IAAI;AAAA,EACvC;AAAA,EACA,QAAQ;AACN,SAAK,UAAU;AACf,SAAK,aAAa,eAAe,KAAK;AAAA,EACxC;AAAA,EACA,aAAa,OAAO,UAAU;AAC5B,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,CAAC,KAAK,QAAQ,cAAe;AACjC,UAAM,EAAE,MAAM,OAAO,IAAI;AACzB,UAAM,oBAAoB,WAAW,KAAK,QAAQ;AAClD,QAAI,CAAC,kBAAmB;AACxB,QAAI,KAAK,cAAc,gBAAgB;AACrC,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AACtD,UAAI,KAAK,cAAc,SAAS;AAC9B,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C,aAAK,aAAa,QAAQ;AAC1B,aAAK,cAAc;AACnB,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAAA,MAChD,WAAW,KAAK,cAAc,QAAQ;AACpC,aAAK,WAAW;AAChB,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,MACzC,WAAW,KAAK,cAAc,WAAW;AACvC,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW;AAChB,eAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,QACxC;AACA,aAAK,cAAc;AACnB,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,MACzC,OAAO;AACL,aAAK,cAAc,IAAI,MAAM,KAAK,SAAS,CAAC;AAAA,MAC9C;AAAA,IACF,WAAW,KAAK,cAAc,+BAA+B,KAAK,cAAc,gBAAgB;AAC9F,YAAM,cAAc,KAAK,aAAa;AACtC,YAAM,iBAAiB,KAAK,aAAa;AACzC,YAAM,YAAY,KAAK,aAAa;AACpC,YAAM,WAAW,KAAK,aAAa;AACnC,YAAM,eAAe,MAAM,KAAK,KAAK,aAAa,UAAU,OAAO,SAAS,GAAG,eAAe,OAAO,SAAS,GAAG;AACjH,WAAK,eAAe,EAAE,GAAG,KAAK,cAAc,GAAG,KAAK,OAAO;AAC3D,UAAI,gBAAgB,KAAK,aAAa,UAAU;AAC9C,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAAA,MAChD;AACA,UAAI,mBAAmB,KAAK,aAAa,aAAa;AACpD,aAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,MAC5C;AACA,UAAI,cAAc,KAAK,aAAa,UAAU,aAAa,KAAK,aAAa,OAAO;AAClF,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAAA,MAC9C;AACA,UAAI,kBAAkB,MAAM,KAAK,KAAK,aAAa,UAAU,OAAO,SAAS,GAAG,eAAe,OAAO,SAAS,GAAG,aAAa;AAC7H,aAAK,cAAc,IAAI,MAAM,UAAU,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,SAAS,QAAQ;AAC5B,QAAI,CAAC,KAAK,QAAQ,cAAe;AACjC,UAAM,UAAU;AAAA,MACd,WAAW;AAAA,MACX;AAAA,MACA,WAAW;AAAA,IACb;AACA,SAAK,QAAQ,cAAc,YAAY,SAAS,UAAU;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAM,QAAQ,KAAK,IAAI;AACvB,aAAO,KAAK,IAAI;AAChB,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,cAAc;AAC7B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,WAAW,GAAG,CAAC;AAAA,QACxC,SAAQ,IAAI,WAAW,GAAG,CAAC,KAAK,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ;AACtJ;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,IAAI,gBAAgB,aAAa,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,QAAQ,QAAQ,QAAQ,GAAI,GAAE,GAAG,IAAI;AAAA,aAChC,QAAQ,MAAO,GAAE,GAAG,IAAI;AAAA,aACxB,OAAO,KAAM,GAAE,GAAG,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AACA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAC7B,GAAG;AACD,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,iBAAiB,OAAO,KAAK;AACpC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,oBAAoB,KAAK;AAAA,EAClC,WAAW,SAAS,QAAQ,OAAO,QAAQ,UAAU,KAAK,QAAQ,GAAG;AACnE,WAAO,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,EACrC;AACA,SAAO,oBAAoB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;AAC3C;AACA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,iBAAiB,QAAQ;AAAA,IAC9B,OAAO;AAAA,MACL,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACH,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,cAAc,GAAG;AAC/E,aAAW,eAAe,OAAO,gBAAgB,kBAAkB;AACrE;AACA,IAAI,+BAA+B;;;ADxXnC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAA;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["React"]}