import React from 'react';
import CustomMediaElement from './wistia-video-element.js';

declare const _default: React.ForwardRefExoticComponent<Omit<React.HTMLAttributes<CustomMediaElement>, "muted" | "defaultMuted" | "disablePictureInPicture" | "height" | "onenterpictureinpicture" | "onleavepictureinpicture" | "playsInline" | "poster" | "videoHeight" | "videoWidth" | "width" | "cancelVideoFrameCallback" | "getVideoPlaybackQuality" | "requestPictureInPicture" | "requestVideoFrameCallback" | "autoplay" | "buffered" | "controls" | "crossOrigin" | "currentSrc" | "currentTime" | "defaultPlaybackRate" | "disableRemotePlayback" | "duration" | "ended" | "error" | "loop" | "mediaKeys" | "networkState" | "onencrypted" | "onwaitingforkey" | "paused" | "playbackRate" | "played" | "preload" | "preservesPitch" | "readyState" | "remote" | "seekable" | "seeking" | "sinkId" | "src" | "srcObject" | "textTracks" | "volume" | "addTextTrack" | "canPlayType" | "fastSeek" | "load" | "pause" | "play" | "setMediaKeys" | "setSinkId" | "NETWORK_EMPTY" | "NETWORK_IDLE" | "NETWORK_LOADING" | "NETWORK_NO_SOURCE" | "HAVE_NOTHING" | "HAVE_METADATA" | "HAVE_CURRENT_DATA" | "HAVE_FUTURE_DATA" | "HAVE_ENOUGH_DATA"> & {} & Partial<Omit<CustomMediaElement, keyof HTMLElement | "connectedCallback" | "disconnectedCallback" | "attributeChangedCallback" | "adoptedCallback">> & React.RefAttributes<CustomMediaElement>>;

export { _default as default };
