{"name": "wistia-video-element", "version": "1.3.3", "description": "A custom element for the Wistia player with an API that matches the `<video>` API", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/wistia-video-element"}, "files": ["wistia-video-element.d.ts", "dist"], "type": "module", "types": "./dist/wistia-video-element.d.ts", "main": "./dist/wistia-video-element.js", "exports": {".": {"types": "./dist/wistia-video-element.d.ts", "import": "./dist/wistia-video-element.js", "require": "./dist/cjs/wistia-video-element.js", "default": "./dist/wistia-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/wistia-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet run", "serve": "wet serve --cors", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "dependencies": {"super-media-element": "~1.4.2"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["wistia", "video", "player", "web component", "custom element"]}