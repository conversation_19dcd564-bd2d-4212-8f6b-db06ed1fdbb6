{"name": "vimeo-video-element", "version": "1.5.3", "description": "A custom element for the Vimeo player with an API that matches the `<video>` API", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/vimeo-video-element"}, "files": ["vimeo-video-element.d.ts", "dist"], "type": "module", "types": "./dist/vimeo-video-element.d.ts", "main": "./dist/vimeo-video-element.js", "exports": {".": {"types": "./dist/vimeo-video-element.d.ts", "import": "./dist/vimeo-video-element.js", "require": "./dist/cjs/vimeo-video-element.js", "default": "./dist/vimeo-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/vimeo-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet run", "serve": "wet serve", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "dependencies": {"@vimeo/player": "2.29.0"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["vimeo", "video", "player", "web component", "custom element"]}