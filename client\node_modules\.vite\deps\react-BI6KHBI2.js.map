{"version": 3, "sources": ["../../wistia-video-element/dist/react.js", "../../super-media-element/super-media-element.js", "../../wistia-video-element/dist/wistia-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./wistia-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"wistia-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "/**\n * Super Media Element\n * Based on https://github.com/muxinc/custom-video-element - Mux - MIT License\n *\n * The goal is to create an element that works just like the video element\n * but can be extended/sub-classed, because native elements cannot be\n * extended today across browsers. Support for extending async loaded video\n * like API's. e.g. video players.\n */\n\n// The onevent like props are weirdly set on the HTMLElement prototype with other\n// generic events making it impossible to pick these specific to HTMLMediaElement.\nexport const Events = [\n  'abort',\n  'canplay',\n  'canplaythrough',\n  'durationchange',\n  'emptied',\n  'encrypted',\n  'ended',\n  'error',\n  'loadeddata',\n  'loadedmetadata',\n  'loadstart',\n  'pause',\n  'play',\n  'playing',\n  'progress',\n  'ratechange',\n  'seeked',\n  'seeking',\n  'stalled',\n  'suspend',\n  'timeupdate',\n  'volumechange',\n  'waiting',\n  'waitingforkey',\n  'resize',\n  'enterpictureinpicture',\n  'leavepictureinpicture',\n  'webkitbeginfullscreen',\n  'webkitendfullscreen',\n  'webkitpresentationmodechanged',\n];\n\nexport const template = globalThis.document?.createElement('template');\n\nif (template) {\n  template.innerHTML = /*html*/`\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n      }\n\n      video,\n      audio {\n        max-width: 100%;\n        max-height: 100%;\n        min-width: 100%;\n        min-height: 100%;\n      }\n    </style>\n    <slot></slot>\n  `;\n}\n\n/**\n * @see https://justinfagnani.com/2015/12/21/real-mixins-with-javascript-classes/\n */\nexport const SuperMediaMixin = (superclass, { tag, is }) => {\n\n  const nativeElTest = globalThis.document?.createElement(tag, { is });\n  const nativeElProps = nativeElTest ? getNativeElProps(nativeElTest) : [];\n\n  return class SuperMedia extends superclass {\n    static Events = Events;\n    static template = template;\n    static skipAttributes = [];\n    static #isDefined;\n\n    static get observedAttributes() {\n      SuperMedia.#define();\n\n      // Include any attributes from the custom built-in.\n      const natAttrs = nativeElTest?.constructor?.observedAttributes ?? [];\n\n      return [\n        ...natAttrs,\n        'autopictureinpicture',\n        'disablepictureinpicture',\n        'disableremoteplayback',\n        'autoplay',\n        'controls',\n        'controlslist',\n        'crossorigin',\n        'loop',\n        'muted',\n        'playsinline',\n        'poster',\n        'preload',\n        'src',\n      ];\n    }\n\n    static #define() {\n      if (this.#isDefined) return;\n      this.#isDefined = true;\n\n      const propsToAttrs = new Set(this.observedAttributes);\n      // defaultMuted maps to the muted attribute, handled manually below.\n      propsToAttrs.delete('muted');\n\n      // Passthrough native el functions from the custom el to the native el\n      for (let prop of nativeElProps) {\n        if (prop in this.prototype) continue;\n\n        const type = typeof nativeElTest[prop];\n        if (type == 'function') {\n          // Function\n          this.prototype[prop] = function (...args) {\n            this.#init();\n\n            const fn = () => {\n              if (this.call) return this.call(prop, ...args);\n              return this.nativeEl[prop].apply(this.nativeEl, args);\n            };\n\n            if (this.loadComplete && !this.isLoaded) {\n              return this.loadComplete.then(fn);\n            }\n            return fn();\n          };\n        } else {\n          // Some properties like src, preload, defaultMuted are handled manually.\n\n          // Getter\n          let config = {\n            get() {\n              this.#init();\n\n              let attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                const val = this.getAttribute(attr);\n                return val === null ? false : val === '' ? true : val;\n              }\n\n              return this.get?.(prop) ?? this.nativeEl?.[prop] ?? this.#standinEl[prop];\n            },\n          };\n\n          if (prop !== prop.toUpperCase()) {\n            // Setter (not a CONSTANT)\n            config.set = async function (val) {\n              this.#init();\n\n              let attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                if (val === true || val === false || val == null) {\n                  this.toggleAttribute(attr, Boolean(val));\n                } else {\n                  this.setAttribute(attr, val);\n                }\n                return;\n              }\n\n              if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n              if (this.set) {\n                this.set(prop, val);\n                return;\n              }\n\n              this.nativeEl[prop] = val;\n            };\n          }\n\n          Object.defineProperty(this.prototype, prop, config);\n        }\n      }\n    }\n\n    #isInit;\n    #loadComplete;\n    #hasLoaded = false;\n    #isLoaded = false;\n    #nativeEl;\n    #standinEl;\n\n    constructor() {\n      super();\n\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: 'open' });\n        this.shadowRoot.append(this.constructor.template.content.cloneNode(true));\n      }\n\n      // If a load method is provided in the child class create a load promise.\n      if (this.load !== SuperMedia.prototype.load) {\n        this.loadComplete = new PublicPromise();\n      }\n\n      // If the custom element is defined before the custom element's HTML is parsed\n      // no attributes will be available in the constructor (construction process).\n      // Wait until initializing in the attributeChangedCallback or\n      // connectedCallback or accessing any properties.\n    }\n\n    get loadComplete() {\n      return this.#loadComplete;\n    }\n\n    set loadComplete(promise) {\n      this.#isLoaded = false;\n      this.#loadComplete = promise;\n      promise?.then(() => {\n        this.#isLoaded = true;\n      });\n    }\n\n    get isLoaded() {\n      return this.#isLoaded;\n    }\n\n    get nativeEl() {\n      return this.#nativeEl\n        ?? this.shadowRoot.querySelector(tag)\n        ?? this.querySelector(tag);\n    }\n\n    set nativeEl(val) {\n      this.#nativeEl = val;\n    }\n\n    get defaultMuted() {\n      return this.hasAttribute('muted');\n    }\n\n    set defaultMuted(val) {\n      this.toggleAttribute('muted', Boolean(val));\n    }\n\n    get src() {\n      return this.getAttribute('src');\n    }\n\n    set src(val) {\n      this.setAttribute('src', `${val}`);\n    }\n\n    get preload() {\n      return this.getAttribute('preload') ?? this.nativeEl?.preload;\n    }\n\n    set preload(val) {\n      this.setAttribute('preload', `${val}`);\n    }\n\n    async #init() {\n      if (this.#isInit) return;\n      this.#isInit = true;\n\n      this.#initStandinEl();\n      this.#initNativeEl();\n\n      for (let prop of nativeElProps)\n        this.#upgradeProperty(prop);\n\n      // Keep some native child elements like track and source in sync.\n      const childMap = new Map();\n      // An unnamed <slot> will be filled with all of the custom element's\n      // top-level child nodes that do not have the slot attribute.\n      const slotEl = this.shadowRoot.querySelector('slot:not([name])');\n      slotEl?.addEventListener('slotchange', () => {\n        const removeNativeChildren = new Map(childMap);\n        slotEl\n          .assignedElements()\n          .filter((el) => ['track', 'source'].includes(el.localName))\n          .forEach(async (el) => {\n            // If the source or track is still in the assigned elements keep it.\n            removeNativeChildren.delete(el);\n            // Re-use clones if possible.\n            let clone = childMap.get(el);\n            if (!clone) {\n              clone = el.cloneNode();\n              childMap.set(el, clone);\n            }\n            if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n            this.nativeEl.append?.(clone);\n          });\n        removeNativeChildren.forEach((el) => el.remove());\n      });\n\n      // The video events are dispatched on the SuperMediaElement instance.\n      // This makes it possible to add event listeners before the element is upgraded.\n      for (let type of this.constructor.Events) {\n        this.shadowRoot.addEventListener?.(type, (evt) => {\n          if (evt.target !== this.nativeEl) return;\n          this.dispatchEvent(new CustomEvent(evt.type, { detail: evt.detail }));\n        }, true);\n      }\n    }\n\n    #upgradeProperty(prop) {\n      // Sets properties that are set before the custom element is upgraded.\n      // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        // Delete the set property from this instance.\n        delete this[prop];\n        // Set the value again via the (prototype) setter on this class.\n        this[prop] = value;\n      }\n    }\n\n    #initStandinEl() {\n      // Neither Chrome or Firefox support setting the muted attribute\n      // after using document.createElement.\n      // Get around this by setting the muted property manually.\n      const dummyEl = document.createElement(tag, { is });\n      dummyEl.muted = this.hasAttribute('muted');\n\n      for (let { name, value } of this.attributes) {\n        dummyEl.setAttribute(name, value);\n      }\n\n      this.#standinEl = {};\n      for (let name of getNativeElProps(dummyEl)) {\n        this.#standinEl[name] = dummyEl[name];\n      }\n\n      // unload dummy video element\n      dummyEl.removeAttribute('src');\n      dummyEl.load();\n    }\n\n    async #initNativeEl() {\n      if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n      // If there is no nativeEl by now, create it our bloody selves.\n      if (!this.nativeEl) {\n        const nativeEl = document.createElement(tag, { is });\n        nativeEl.part = tag;\n        this.shadowRoot.append(nativeEl);\n      }\n\n      // Neither Chrome or Firefox support setting the muted attribute\n      // after using document.createElement.\n      // Get around this by setting the muted property manually.\n      this.nativeEl.muted = this.hasAttribute('muted');\n    }\n\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      // Initialize right after construction when the attributes become available.\n      this.#init();\n\n      // Only call loadSrc when the super class has a load method.\n      if (attrName === 'src' && this.load !== SuperMedia.prototype.load) {\n        this.#loadSrc();\n      }\n\n      this.#forwardAttribute(attrName, oldValue, newValue);\n    }\n\n    async #loadSrc() {\n      // The first time we use the Promise created in the constructor.\n      if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n      this.#hasLoaded = true;\n\n      // Wait 1 tick to allow other attributes to be set.\n      await Promise.resolve();\n      await this.load();\n\n      this.loadComplete?.resolve();\n      await this.loadComplete;\n    }\n\n    async #forwardAttribute(attrName, oldValue, newValue) {\n      if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n      // Ignore a few that don't need to be passed & skipped attributes.\n      // e.g. src: native element is using MSE and has a blob url as src attribute.\n      if (['id', 'class', ...this.constructor.skipAttributes].includes(attrName)) {\n        return;\n      }\n\n      if (newValue === null) {\n        this.nativeEl.removeAttribute?.(attrName);\n      } else {\n        this.nativeEl.setAttribute?.(attrName, newValue);\n      }\n    }\n\n    connectedCallback() {\n      this.#init();\n    }\n  };\n};\n\nfunction getNativeElProps(nativeElTest) {\n  // Map all native element properties to the custom element\n  // so that they're applied to the native element.\n  // Skipping HTMLElement because of things like \"attachShadow\"\n  // causing issues. Most of those props still need to apply to\n  // the custom element.\n  let nativeElProps = [];\n\n  // Walk the prototype chain up to HTMLElement.\n  // This will grab all super class props in between.\n  // i.e. VideoElement and MediaElement\n  for (\n    let proto = Object.getPrototypeOf(nativeElTest);\n    proto && proto !== HTMLElement.prototype;\n    proto = Object.getPrototypeOf(proto)\n  ) {\n    nativeElProps.push(...Object.getOwnPropertyNames(proto));\n  }\n\n  return nativeElProps;\n}\n\n/**\n * A utility to create Promises with convenient public resolve and reject methods.\n * @return {Promise}\n */\nclass PublicPromise extends Promise {\n  constructor(executor = () => {}) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\n\nexport const SuperVideoElement = globalThis.document ? SuperMediaMixin(HTMLElement, { tag: 'video' }) : class {};\n\nexport const SuperAudioElement = globalThis.document ? SuperMediaMixin(HTMLElement, { tag: 'audio' }) : class {};\n", "var _a, _b;\nimport { SuperVideoElement } from \"super-media-element\";\nconst templateLightDOM = (_a = globalThis.document) == null ? void 0 : _a.createElement(\"template\");\nif (templateLightDOM) {\n  templateLightDOM.innerHTML = /*html*/\n  `\n  <div class=\"wistia_embed\"></div>\n  `;\n}\nconst templateShadowDOM = (_b = globalThis.document) == null ? void 0 : _b.createElement(\"template\");\nif (templateShadowDOM) {\n  templateShadowDOM.innerHTML = /*html*/\n  `\n  <style>\n    :host {\n      display: inline-block;\n      min-width: 300px;\n      min-height: 150px;\n      position: relative;\n    }\n    ::slotted(.wistia_embed) {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n    }\n  </style>\n  <slot></slot>\n  `;\n}\nclass WistiaVideoElement extends SuperVideoElement {\n  static template = templateShadowDOM;\n  static skipAttributes = [\"src\"];\n  get nativeEl() {\n    var _a2;\n    return ((_a2 = this.api) == null ? void 0 : _a2.elem()) ?? this.querySelector(\"video\");\n  }\n  async load() {\n    var _a2;\n    (_a2 = this.querySelector(\".wistia_embed\")) == null ? void 0 : _a2.remove();\n    if (!this.src) {\n      return;\n    }\n    await new Promise((resolve) => setTimeout(resolve, 50));\n    const MATCH_SRC = /(?:wistia\\.com|wi\\.st)\\/(?:medias|embed)\\/(.*)$/i;\n    const id = this.src.match(MATCH_SRC)[1];\n    const options = {\n      autoPlay: this.autoplay,\n      preload: this.preload ?? \"metadata\",\n      playsinline: this.playsInline,\n      endVideoBehavior: this.loop && \"loop\",\n      chromeless: !this.controls,\n      playButton: this.controls,\n      muted: this.defaultMuted\n    };\n    this.append(templateLightDOM.content.cloneNode(true));\n    const div = this.querySelector(\".wistia_embed\");\n    if (!div.id) div.id = uniqueId(id);\n    div.classList.add(`wistia_async_${id}`);\n    const scriptUrl = \"https://fast.wistia.com/assets/external/E-v1.js\";\n    await loadScript(scriptUrl, \"Wistia\");\n    this.api = await new Promise((onReady) => {\n      globalThis._wq.push({\n        id: div.id,\n        onReady,\n        options\n      });\n    });\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (attrName === \"controls\") {\n      await this.loadComplete;\n      switch (attrName) {\n        case \"controls\":\n          this.api.bigPlayButtonEnabled(this.controls);\n          this.controls ? this.api.releaseChromeless() : this.api.requestChromeless();\n          break;\n      }\n      return;\n    }\n    super.attributeChangedCallback(attrName, oldValue, newValue);\n  }\n  // Override some methods w/ defaults if the video element is not ready yet when called.\n  // Some methods require the Wistia API instead of the native video element API.\n  get duration() {\n    var _a2;\n    return (_a2 = this.api) == null ? void 0 : _a2.duration();\n  }\n  play() {\n    this.api.play();\n    return new Promise((resolve) => this.addEventListener(\"playing\", resolve));\n  }\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName) {\n  if (!globalName) return import(\n    /* webpackIgnore: true */\n    src\n  );\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (self[globalName]) return self[globalName];\n  return loadScriptCache[src] = new Promise((resolve, reject) => {\n    const script = document.createElement(\"script\");\n    script.defer = true;\n    script.src = src;\n    script.onload = () => resolve(self[globalName]);\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nlet idCounter = 0;\nfunction uniqueId(prefix) {\n  const id = ++idCounter;\n  return `${prefix}${id}`;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"wistia-video\")) {\n  globalThis.customElements.define(\"wistia-video\", WistiaVideoElement);\n}\nvar wistia_video_element_default = WistiaVideoElement;\nexport {\n  wistia_video_element_default as default,\n  uniqueId\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACSX,IAAM,SAAS;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,WAAW,WAAW,UAAU,cAAc,UAAU;AAErE,IAAI,UAAU;AACZ,WAAS;AAAA,EAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB/B;AAKO,IAAM,kBAAkB,CAAC,YAAY,EAAE,KAAK,GAAG,MAAM;AAE1D,QAAM,eAAe,WAAW,UAAU,cAAc,KAAK,EAAE,GAAG,CAAC;AACnE,QAAM,gBAAgB,eAAe,iBAAiB,YAAY,IAAI,CAAC;AAEvE,SAAO,MAAM,mBAAmB,WAAW;AAAA,IACzC,OAAO,SAAS;AAAA,IAChB,OAAO,WAAW;AAAA,IAClB,OAAO,iBAAiB,CAAC;AAAA,IACzB,OAAO;AAAA,IAEP,WAAW,qBAAqB;AAC9B,iBAAW,QAAQ;AAGnB,YAAM,WAAW,cAAc,aAAa,sBAAsB,CAAC;AAEnE,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,OAAO,UAAU;AACf,UAAI,KAAK,WAAY;AACrB,WAAK,aAAa;AAElB,YAAM,eAAe,IAAI,IAAI,KAAK,kBAAkB;AAEpD,mBAAa,OAAO,OAAO;AAG3B,eAAS,QAAQ,eAAe;AAC9B,YAAI,QAAQ,KAAK,UAAW;AAE5B,cAAM,OAAO,OAAO,aAAa,IAAI;AACrC,YAAI,QAAQ,YAAY;AAEtB,eAAK,UAAU,IAAI,IAAI,YAAa,MAAM;AACxC,iBAAK,MAAM;AAEX,kBAAM,KAAK,MAAM;AACf,kBAAI,KAAK,KAAM,QAAO,KAAK,KAAK,MAAM,GAAG,IAAI;AAC7C,qBAAO,KAAK,SAAS,IAAI,EAAE,MAAM,KAAK,UAAU,IAAI;AAAA,YACtD;AAEA,gBAAI,KAAK,gBAAgB,CAAC,KAAK,UAAU;AACvC,qBAAO,KAAK,aAAa,KAAK,EAAE;AAAA,YAClC;AACA,mBAAO,GAAG;AAAA,UACZ;AAAA,QACF,OAAO;AAIL,cAAI,SAAS;AAAA,YACX,MAAM;AACJ,mBAAK,MAAM;AAEX,kBAAI,OAAO,KAAK,YAAY;AAC5B,kBAAI,aAAa,IAAI,IAAI,GAAG;AAC1B,sBAAM,MAAM,KAAK,aAAa,IAAI;AAClC,uBAAO,QAAQ,OAAO,QAAQ,QAAQ,KAAK,OAAO;AAAA,cACpD;AAEA,qBAAO,KAAK,MAAM,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,WAAW,IAAI;AAAA,YAC1E;AAAA,UACF;AAEA,cAAI,SAAS,KAAK,YAAY,GAAG;AAE/B,mBAAO,MAAM,eAAgB,KAAK;AAChC,mBAAK,MAAM;AAEX,kBAAI,OAAO,KAAK,YAAY;AAC5B,kBAAI,aAAa,IAAI,IAAI,GAAG;AAC1B,oBAAI,QAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAChD,uBAAK,gBAAgB,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACzC,OAAO;AACL,uBAAK,aAAa,MAAM,GAAG;AAAA,gBAC7B;AACA;AAAA,cACF;AAEA,kBAAI,KAAK,gBAAgB,CAAC,KAAK,SAAU,OAAM,KAAK;AAEpD,kBAAI,KAAK,KAAK;AACZ,qBAAK,IAAI,MAAM,GAAG;AAClB;AAAA,cACF;AAEA,mBAAK,SAAS,IAAI,IAAI;AAAA,YACxB;AAAA,UACF;AAEA,iBAAO,eAAe,KAAK,WAAW,MAAM,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,IAEA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IAEA,cAAc;AACZ,YAAM;AAEN,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAClC,aAAK,WAAW,OAAO,KAAK,YAAY,SAAS,QAAQ,UAAU,IAAI,CAAC;AAAA,MAC1E;AAGA,UAAI,KAAK,SAAS,WAAW,UAAU,MAAM;AAC3C,aAAK,eAAe,IAAI,cAAc;AAAA,MACxC;AAAA,IAMF;AAAA,IAEA,IAAI,eAAe;AACjB,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,IAAI,aAAa,SAAS;AACxB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrB,eAAS,KAAK,MAAM;AAClB,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,IAEA,IAAI,WAAW;AACb,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,IAAI,WAAW;AACb,aAAO,KAAK,aACP,KAAK,WAAW,cAAc,GAAG,KACjC,KAAK,cAAc,GAAG;AAAA,IAC7B;AAAA,IAEA,IAAI,SAAS,KAAK;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,IAEA,IAAI,eAAe;AACjB,aAAO,KAAK,aAAa,OAAO;AAAA,IAClC;AAAA,IAEA,IAAI,aAAa,KAAK;AACpB,WAAK,gBAAgB,SAAS,QAAQ,GAAG,CAAC;AAAA,IAC5C;AAAA,IAEA,IAAI,MAAM;AACR,aAAO,KAAK,aAAa,KAAK;AAAA,IAChC;AAAA,IAEA,IAAI,IAAI,KAAK;AACX,WAAK,aAAa,OAAO,GAAG,GAAG,EAAE;AAAA,IACnC;AAAA,IAEA,IAAI,UAAU;AACZ,aAAO,KAAK,aAAa,SAAS,KAAK,KAAK,UAAU;AAAA,IACxD;AAAA,IAEA,IAAI,QAAQ,KAAK;AACf,WAAK,aAAa,WAAW,GAAG,GAAG,EAAE;AAAA,IACvC;AAAA,IAEA,MAAM,QAAQ;AACZ,UAAI,KAAK,QAAS;AAClB,WAAK,UAAU;AAEf,WAAK,eAAe;AACpB,WAAK,cAAc;AAEnB,eAAS,QAAQ;AACf,aAAK,iBAAiB,IAAI;AAG5B,YAAM,WAAW,oBAAI,IAAI;AAGzB,YAAM,SAAS,KAAK,WAAW,cAAc,kBAAkB;AAC/D,cAAQ,iBAAiB,cAAc,MAAM;AAC3C,cAAM,uBAAuB,IAAI,IAAI,QAAQ;AAC7C,eACG,iBAAiB,EACjB,OAAO,CAAC,OAAO,CAAC,SAAS,QAAQ,EAAE,SAAS,GAAG,SAAS,CAAC,EACzD,QAAQ,OAAO,OAAO;AAErB,+BAAqB,OAAO,EAAE;AAE9B,cAAI,QAAQ,SAAS,IAAI,EAAE;AAC3B,cAAI,CAAC,OAAO;AACV,oBAAQ,GAAG,UAAU;AACrB,qBAAS,IAAI,IAAI,KAAK;AAAA,UACxB;AACA,cAAI,KAAK,gBAAgB,CAAC,KAAK,SAAU,OAAM,KAAK;AACpD,eAAK,SAAS,SAAS,KAAK;AAAA,QAC9B,CAAC;AACH,6BAAqB,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;AAAA,MAClD,CAAC;AAID,eAAS,QAAQ,KAAK,YAAY,QAAQ;AACxC,aAAK,WAAW,mBAAmB,MAAM,CAAC,QAAQ;AAChD,cAAI,IAAI,WAAW,KAAK,SAAU;AAClC,eAAK,cAAc,IAAI,YAAY,IAAI,MAAM,EAAE,QAAQ,IAAI,OAAO,CAAC,CAAC;AAAA,QACtE,GAAG,IAAI;AAAA,MACT;AAAA,IACF;AAAA,IAEA,iBAAiB,MAAM;AAGrB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,cAAM,QAAQ,KAAK,IAAI;AAEvB,eAAO,KAAK,IAAI;AAEhB,aAAK,IAAI,IAAI;AAAA,MACf;AAAA,IACF;AAAA,IAEA,iBAAiB;AAIf,YAAM,UAAU,SAAS,cAAc,KAAK,EAAE,GAAG,CAAC;AAClD,cAAQ,QAAQ,KAAK,aAAa,OAAO;AAEzC,eAAS,EAAE,MAAM,MAAM,KAAK,KAAK,YAAY;AAC3C,gBAAQ,aAAa,MAAM,KAAK;AAAA,MAClC;AAEA,WAAK,aAAa,CAAC;AACnB,eAAS,QAAQ,iBAAiB,OAAO,GAAG;AAC1C,aAAK,WAAW,IAAI,IAAI,QAAQ,IAAI;AAAA,MACtC;AAGA,cAAQ,gBAAgB,KAAK;AAC7B,cAAQ,KAAK;AAAA,IACf;AAAA,IAEA,MAAM,gBAAgB;AACpB,UAAI,KAAK,gBAAgB,CAAC,KAAK,SAAU,OAAM,KAAK;AAGpD,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,WAAW,SAAS,cAAc,KAAK,EAAE,GAAG,CAAC;AACnD,iBAAS,OAAO;AAChB,aAAK,WAAW,OAAO,QAAQ;AAAA,MACjC;AAKA,WAAK,SAAS,QAAQ,KAAK,aAAa,OAAO;AAAA,IACjD;AAAA,IAEA,yBAAyB,UAAU,UAAU,UAAU;AAErD,WAAK,MAAM;AAGX,UAAI,aAAa,SAAS,KAAK,SAAS,WAAW,UAAU,MAAM;AACjE,aAAK,SAAS;AAAA,MAChB;AAEA,WAAK,kBAAkB,UAAU,UAAU,QAAQ;AAAA,IACrD;AAAA,IAEA,MAAM,WAAW;AAEf,UAAI,KAAK,WAAY,MAAK,eAAe,IAAI,cAAc;AAC3D,WAAK,aAAa;AAGlB,YAAM,QAAQ,QAAQ;AACtB,YAAM,KAAK,KAAK;AAEhB,WAAK,cAAc,QAAQ;AAC3B,YAAM,KAAK;AAAA,IACb;AAAA,IAEA,MAAM,kBAAkB,UAAU,UAAU,UAAU;AACpD,UAAI,KAAK,gBAAgB,CAAC,KAAK,SAAU,OAAM,KAAK;AAIpD,UAAI,CAAC,MAAM,SAAS,GAAG,KAAK,YAAY,cAAc,EAAE,SAAS,QAAQ,GAAG;AAC1E;AAAA,MACF;AAEA,UAAI,aAAa,MAAM;AACrB,aAAK,SAAS,kBAAkB,QAAQ;AAAA,MAC1C,OAAO;AACL,aAAK,SAAS,eAAe,UAAU,QAAQ;AAAA,MACjD;AAAA,IACF;AAAA,IAEA,oBAAoB;AAClB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,cAAc;AAMtC,MAAI,gBAAgB,CAAC;AAKrB,WACM,QAAQ,OAAO,eAAe,YAAY,GAC9C,SAAS,UAAU,YAAY,WAC/B,QAAQ,OAAO,eAAe,KAAK,GACnC;AACA,kBAAc,KAAK,GAAG,OAAO,oBAAoB,KAAK,CAAC;AAAA,EACzD;AAEA,SAAO;AACT;AAMA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAAC,GAAG;AAC/B,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AAEO,IAAM,oBAAoB,WAAW,WAAW,gBAAgB,aAAa,EAAE,KAAK,QAAQ,CAAC,IAAI,MAAM;AAAC;AAExG,IAAM,oBAAoB,WAAW,WAAW,gBAAgB,aAAa,EAAE,KAAK,QAAQ,CAAC,IAAI,MAAM;AAAC;;;ACxb/G,IAAI;AAAJ,IAAQ;AAER,IAAM,oBAAoB,KAAK,WAAW,aAAa,OAAO,SAAS,GAAG,cAAc,UAAU;AAClG,IAAI,kBAAkB;AACpB,mBAAiB;AAAA,EACjB;AAAA;AAAA;AAGF;AACA,IAAM,qBAAqB,KAAK,WAAW,aAAa,OAAO,SAAS,GAAG,cAAc,UAAU;AACnG,IAAI,mBAAmB;AACrB,oBAAkB;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBF;AACA,IAAM,qBAAN,cAAiC,kBAAkB;AAAA,EACjD,OAAO,WAAW;AAAA,EAClB,OAAO,iBAAiB,CAAC,KAAK;AAAA,EAC9B,IAAI,WAAW;AACb,QAAI;AACJ,aAAS,MAAM,KAAK,QAAQ,OAAO,SAAS,IAAI,KAAK,MAAM,KAAK,cAAc,OAAO;AAAA,EACvF;AAAA,EACA,MAAM,OAAO;AACX,QAAI;AACJ,KAAC,MAAM,KAAK,cAAc,eAAe,MAAM,OAAO,SAAS,IAAI,OAAO;AAC1E,QAAI,CAAC,KAAK,KAAK;AACb;AAAA,IACF;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AACtD,UAAM,YAAY;AAClB,UAAM,KAAK,KAAK,IAAI,MAAM,SAAS,EAAE,CAAC;AACtC,UAAM,UAAU;AAAA,MACd,UAAU,KAAK;AAAA,MACf,SAAS,KAAK,WAAW;AAAA,MACzB,aAAa,KAAK;AAAA,MAClB,kBAAkB,KAAK,QAAQ;AAAA,MAC/B,YAAY,CAAC,KAAK;AAAA,MAClB,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,IACd;AACA,SAAK,OAAO,iBAAiB,QAAQ,UAAU,IAAI,CAAC;AACpD,UAAM,MAAM,KAAK,cAAc,eAAe;AAC9C,QAAI,CAAC,IAAI,GAAI,KAAI,KAAK,SAAS,EAAE;AACjC,QAAI,UAAU,IAAI,gBAAgB,EAAE,EAAE;AACtC,UAAM,YAAY;AAClB,UAAM,WAAW,WAAW,QAAQ;AACpC,SAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,YAAY;AACxC,iBAAW,IAAI,KAAK;AAAA,QAClB,IAAI,IAAI;AAAA,QACR;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,MAAM,yBAAyB,UAAU,UAAU,UAAU;AAC3D,QAAI,aAAa,YAAY;AAC3B,YAAM,KAAK;AACX,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,eAAK,IAAI,qBAAqB,KAAK,QAAQ;AAC3C,eAAK,WAAW,KAAK,IAAI,kBAAkB,IAAI,KAAK,IAAI,kBAAkB;AAC1E;AAAA,MACJ;AACA;AAAA,IACF;AACA,UAAM,yBAAyB,UAAU,UAAU,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA,EAGA,IAAI,WAAW;AACb,QAAI;AACJ,YAAQ,MAAM,KAAK,QAAQ,OAAO,SAAS,IAAI,SAAS;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,IAAI,KAAK;AACd,WAAO,IAAI,QAAQ,CAAC,YAAY,KAAK,iBAAiB,WAAW,OAAO,CAAC;AAAA,EAC3E;AACF;AACA,IAAM,kBAAkB,CAAC;AACzB,eAAe,WAAW,KAAK,YAAY;AACzC,MAAI,CAAC,WAAY,QAAO;AAAA;AAAA,IAEtB;AAAA;AAEF,MAAI,gBAAgB,GAAG,EAAG,QAAO,gBAAgB,GAAG;AACpD,MAAI,KAAK,UAAU,EAAG,QAAO,KAAK,UAAU;AAC5C,SAAO,gBAAgB,GAAG,IAAI,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7D,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,QAAQ;AACf,WAAO,MAAM;AACb,WAAO,SAAS,MAAM,QAAQ,KAAK,UAAU,CAAC;AAC9C,WAAO,UAAU;AACjB,aAAS,KAAK,OAAO,MAAM;AAAA,EAC7B,CAAC;AACH;AACA,IAAI,YAAY;AAChB,SAAS,SAAS,QAAQ;AACxB,QAAM,KAAK,EAAE;AACb,SAAO,GAAG,MAAM,GAAG,EAAE;AACvB;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,cAAc,GAAG;AAC/E,aAAW,eAAe,OAAO,gBAAgB,kBAAkB;AACrE;AACA,IAAI,+BAA+B;;;AF9GnC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAIA,KAAIC;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQD,MAAK,WAAW,gBAAgB,OAAO,SAASA,IAAG,cAAc,CAAC,OAAO,GAAGC,MAAK,aAAa,uBAAuB,OAAO,SAASA,IAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAID;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAWA,MAAK,WAAW,gBAAgB,OAAO,SAASA,IAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAE;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["_a", "_b", "React"]}