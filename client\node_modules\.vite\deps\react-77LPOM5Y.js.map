{"version": 3, "sources": ["../../spotify-audio-element/dist/react.js", "../../spotify-audio-element/dist/spotify-audio-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./spotify-audio-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"spotify-audio\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "const EMBED_BASE = \"https://open.spotify.com\";\nconst MATCH_SRC = /open\\.spotify\\.com\\/(\\w+)\\/(\\w+)/i;\nconst API_URL = \"https://open.spotify.com/embed-podcast/iframe-api/v1\";\nconst API_GLOBAL = \"SpotifyIframeApi\";\nconst API_GLOBAL_READY = \"onSpotifyIframeApiReady\";\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    scrolling: \"no\",\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 160px;\n        min-height: 80px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        overflow: hidden;\n      }\n      :host(:not([controls])) {\n        display: none !important;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  var _a, _b, _c;\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const type = matches && matches[1];\n  const metaId = matches && matches[2];\n  const params = {\n    t: (_a = props.config) == null ? void 0 : _a.startAt,\n    theme: ((_b = props.config) == null ? void 0 : _b.theme) === \"dark\" ? \"0\" : null\n  };\n  const videoPath = ((_c = props.config) == null ? void 0 : _c.preferVideo) ? \"/video\" : \"\";\n  return `${EMBED_BASE}/embed/${type}/${metaId}${videoPath}?${serialize(params)}`;\n}\nclass SpotifyAudioElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"controls\",\n    \"loop\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #isInit;\n  #isWaiting = false;\n  #closeToEnded = false;\n  #paused = true;\n  #currentTime = 0;\n  #duration = NaN;\n  #seeking = false;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  async load() {\n    var _a, _b, _c;\n    if (this.#loadRequested) return;\n    if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#isWaiting = false;\n    this.#closeToEnded = false;\n    this.#currentTime = 0;\n    this.#duration = NaN;\n    this.#seeking = false;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    const options = {\n      t: (_a = this.config) == null ? void 0 : _a.startAt,\n      theme: ((_b = this.config) == null ? void 0 : _b.theme) === \"dark\" ? \"0\" : null,\n      preferVideo: (_c = this.config) == null ? void 0 : _c.preferVideo\n    };\n    if (this.#isInit) {\n      this.api = oldApi;\n      this.api.iframeElement.src = serializeIframeUrl(namedNodeMapToObject(this.attributes), this);\n    } else {\n      this.#isInit = true;\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: \"open\" });\n        this.shadowRoot.innerHTML = getTemplateHTML(namedNodeMapToObject(this.attributes), this);\n      }\n      let iframe = this.shadowRoot.querySelector(\"iframe\");\n      const Spotify = await loadScript(API_URL, API_GLOBAL, API_GLOBAL_READY);\n      this.api = await new Promise((resolve) => Spotify.createController(iframe, options, resolve));\n      this.api.iframeElement = iframe;\n      this.api.addListener(\"ready\", () => {\n        this.dispatchEvent(new Event(\"loadedmetadata\"));\n        this.dispatchEvent(new Event(\"durationchange\"));\n        this.dispatchEvent(new Event(\"volumechange\"));\n      });\n      this.api.addListener(\"playback_update\", (event) => {\n        if (this.#closeToEnded && this.#paused && (event.data.isBuffering || !event.data.isPaused)) {\n          this.#closeToEnded = false;\n          this.currentTime = 1;\n          return;\n        }\n        if (event.data.duration / 1e3 !== this.#duration) {\n          this.#closeToEnded = false;\n          this.#duration = event.data.duration / 1e3;\n          this.dispatchEvent(new Event(\"durationchange\"));\n        }\n        if (event.data.position / 1e3 !== this.#currentTime) {\n          this.#seeking = false;\n          this.#closeToEnded = false;\n          this.#currentTime = event.data.position / 1e3;\n          this.dispatchEvent(new Event(\"timeupdate\"));\n        }\n        if (!this.#isWaiting && !this.#paused && event.data.isPaused) {\n          this.#paused = true;\n          this.dispatchEvent(new Event(\"pause\"));\n          return;\n        }\n        if (this.#paused && (event.data.isBuffering || !event.data.isPaused)) {\n          this.#paused = false;\n          this.dispatchEvent(new Event(\"play\"));\n          this.#isWaiting = event.data.isBuffering;\n          if (this.#isWaiting) {\n            this.dispatchEvent(new Event(\"waiting\"));\n          } else {\n            this.dispatchEvent(new Event(\"playing\"));\n          }\n          return;\n        }\n        if (this.#isWaiting && !event.data.isPaused) {\n          this.#isWaiting = false;\n          this.dispatchEvent(new Event(\"playing\"));\n          return;\n        }\n        if (!this.paused && !this.seeking && !this.#closeToEnded && Math.ceil(this.currentTime) >= this.duration) {\n          this.#closeToEnded = true;\n          if (this.loop) {\n            this.currentTime = 1;\n            return;\n          }\n          if (!this.continuous) {\n            this.pause();\n            this.dispatchEvent(new Event(\"ended\"));\n          }\n          return;\n        }\n      });\n    }\n    this.loadComplete.resolve();\n    await this.loadComplete;\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#paused = false;\n    this.#isWaiting = true;\n    this.dispatchEvent(new Event(\"play\"));\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.resume();\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pause();\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  get paused() {\n    return this.#paused ?? true;\n  }\n  get muted() {\n    return false;\n  }\n  set muted(val) {\n  }\n  get volume() {\n    return 1;\n  }\n  set volume(val) {\n  }\n  get ended() {\n    return Math.ceil(this.currentTime) >= this.duration;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#seeking = true;\n    let oldTime = this.#currentTime;\n    this.#currentTime = val;\n    this.dispatchEvent(new Event(\"timeupdate\"));\n    this.#currentTime = oldTime;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.seek(val);\n    });\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    this.setAttribute(\"src\", `${val}`);\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${key}`;\n    else html += ` ${key}=\"${value}\"`;\n  }\n  return html;\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName, readyFnName) {\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (globalName && self[globalName]) {\n    return Promise.resolve(self[globalName]);\n  }\n  return loadScriptCache[src] = new Promise(function(resolve, reject) {\n    const script = document.createElement(\"script\");\n    script.src = src;\n    const ready = (api) => resolve(api);\n    if (readyFnName) self[readyFnName] = ready;\n    script.onload = () => !readyFnName && ready();\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"spotify-audio\")) {\n  globalThis.customElements.define(\"spotify-audio\", SpotifyAudioElement);\n}\nvar spotify_audio_element_default = SpotifyAudioElement;\nexport {\n  spotify_audio_element_default as default\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACHlB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,SAAS,gBAAgB,OAAO,QAAQ,CAAC,GAAG;AAC1C,QAAM,cAAc;AAAA,IAClB,KAAK,mBAAmB,OAAO,KAAK;AAAA,IACpC,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAoBS,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAG7C;AACA,SAAS,mBAAmB,OAAO,OAAO;AACxC,MAAI,IAAI,IAAI;AACZ,MAAI,CAAC,MAAM,IAAK;AAChB,QAAM,UAAU,MAAM,IAAI,MAAM,SAAS;AACzC,QAAM,OAAO,WAAW,QAAQ,CAAC;AACjC,QAAM,SAAS,WAAW,QAAQ,CAAC;AACnC,QAAM,SAAS;AAAA,IACb,IAAI,KAAK,MAAM,WAAW,OAAO,SAAS,GAAG;AAAA,IAC7C,SAAS,KAAK,MAAM,WAAW,OAAO,SAAS,GAAG,WAAW,SAAS,MAAM;AAAA,EAC9E;AACA,QAAM,cAAc,KAAK,MAAM,WAAW,OAAO,SAAS,GAAG,eAAe,WAAW;AACvF,SAAO,GAAG,UAAU,UAAU,IAAI,IAAI,MAAM,GAAG,SAAS,IAAI,UAAU,MAAM,CAAC;AAC/E;AACA,IAAM,sBAAN,eAAmC,WAAW,eAAe,MAAM;AACnE,GAAG;AAAA,EACD,OAAO,kBAAkB;AAAA,EACzB,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,EAC1C,OAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe,IAAI,cAAc;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,MAAM,OAAO;AACX,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,eAAgB;AACzB,QAAI,KAAK,WAAY,MAAK,eAAe,IAAI,cAAc;AAC3D,SAAK,aAAa;AAClB,WAAO,KAAK,iBAAiB,QAAQ,QAAQ;AAC7C,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC,QAAI,SAAS,KAAK;AAClB,SAAK,MAAM;AACX,QAAI,CAAC,KAAK,KAAK;AACb;AAAA,IACF;AACA,SAAK,cAAc,IAAI,MAAM,WAAW,CAAC;AACzC,UAAM,UAAU;AAAA,MACd,IAAI,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG;AAAA,MAC5C,SAAS,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,WAAW,SAAS,MAAM;AAAA,MAC3E,cAAc,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG;AAAA,IACxD;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,MAAM;AACX,WAAK,IAAI,cAAc,MAAM,mBAAmB,qBAAqB,KAAK,UAAU,GAAG,IAAI;AAAA,IAC7F,OAAO;AACL,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAClC,aAAK,WAAW,YAAY,gBAAgB,qBAAqB,KAAK,UAAU,GAAG,IAAI;AAAA,MACzF;AACA,UAAI,SAAS,KAAK,WAAW,cAAc,QAAQ;AACnD,YAAM,UAAU,MAAM,WAAW,SAAS,YAAY,gBAAgB;AACtE,WAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,YAAY,QAAQ,iBAAiB,QAAQ,SAAS,OAAO,CAAC;AAC5F,WAAK,IAAI,gBAAgB;AACzB,WAAK,IAAI,YAAY,SAAS,MAAM;AAClC,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAAA,MAC9C,CAAC;AACD,WAAK,IAAI,YAAY,mBAAmB,CAAC,UAAU;AACjD,YAAI,KAAK,iBAAiB,KAAK,YAAY,MAAM,KAAK,eAAe,CAAC,MAAM,KAAK,WAAW;AAC1F,eAAK,gBAAgB;AACrB,eAAK,cAAc;AACnB;AAAA,QACF;AACA,YAAI,MAAM,KAAK,WAAW,QAAQ,KAAK,WAAW;AAChD,eAAK,gBAAgB;AACrB,eAAK,YAAY,MAAM,KAAK,WAAW;AACvC,eAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAAA,QAChD;AACA,YAAI,MAAM,KAAK,WAAW,QAAQ,KAAK,cAAc;AACnD,eAAK,WAAW;AAChB,eAAK,gBAAgB;AACrB,eAAK,eAAe,MAAM,KAAK,WAAW;AAC1C,eAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,QAC5C;AACA,YAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,MAAM,KAAK,UAAU;AAC5D,eAAK,UAAU;AACf,eAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AACrC;AAAA,QACF;AACA,YAAI,KAAK,YAAY,MAAM,KAAK,eAAe,CAAC,MAAM,KAAK,WAAW;AACpE,eAAK,UAAU;AACf,eAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AACpC,eAAK,aAAa,MAAM,KAAK;AAC7B,cAAI,KAAK,YAAY;AACnB,iBAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,UACzC,OAAO;AACL,iBAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,UACzC;AACA;AAAA,QACF;AACA,YAAI,KAAK,cAAc,CAAC,MAAM,KAAK,UAAU;AAC3C,eAAK,aAAa;AAClB,eAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC;AAAA,QACF;AACA,YAAI,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW,CAAC,KAAK,iBAAiB,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,UAAU;AACxG,eAAK,gBAAgB;AACrB,cAAI,KAAK,MAAM;AACb,iBAAK,cAAc;AACnB;AAAA,UACF;AACA,cAAI,CAAC,KAAK,YAAY;AACpB,iBAAK,MAAM;AACX,iBAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,UACvC;AACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,aAAa,QAAQ;AAC1B,UAAM,KAAK;AAAA,EACb;AAAA,EACA,MAAM,yBAAyB,UAAU,UAAU,UAAU;AAC3D,QAAI,aAAa,SAAU;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK,OAAO;AACV,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,QAAI;AACJ,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AACpC,UAAM,KAAK;AACX,YAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,OAAO;AAAA,EACtD;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI;AACJ,UAAM,KAAK;AACX,YAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,MAAM;AAAA,EACrD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO;AAAA,EACT;AAAA,EACA,IAAI,MAAM,KAAK;AAAA,EACf;AAAA,EACA,IAAI,SAAS;AACX,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa,MAAM;AAAA,EACjC;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,QAAI,KAAK,QAAQ,IAAK;AACtB,SAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC3C;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK,eAAe,IAAK;AAC7B,SAAK,WAAW;AAChB,QAAI,UAAU,KAAK;AACnB,SAAK,eAAe;AACpB,SAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAC1C,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,KAAK,GAAG;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,aAAa,OAAO,GAAG,GAAG,EAAE;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAM,QAAQ,KAAK,IAAI;AACvB,aAAO,KAAK,IAAI;AAChB,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,GAAG;AAAA,QAC5B,SAAQ,IAAI,GAAG,KAAK,KAAK;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,IAAI,gBAAgB,aAAa,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,QAAQ,QAAQ,QAAQ,GAAI,GAAE,GAAG,IAAI;AAAA,aAChC,QAAQ,MAAO,GAAE,GAAG,IAAI;AAAA,aACxB,OAAO,KAAM,GAAE,GAAG,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,cAAc;AAC7B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC;AACzB,eAAe,WAAW,KAAK,YAAY,aAAa;AACtD,MAAI,gBAAgB,GAAG,EAAG,QAAO,gBAAgB,GAAG;AACpD,MAAI,cAAc,KAAK,UAAU,GAAG;AAClC,WAAO,QAAQ,QAAQ,KAAK,UAAU,CAAC;AAAA,EACzC;AACA,SAAO,gBAAgB,GAAG,IAAI,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAClE,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,UAAM,QAAQ,CAAC,QAAQ,QAAQ,GAAG;AAClC,QAAI,YAAa,MAAK,WAAW,IAAI;AACrC,WAAO,SAAS,MAAM,CAAC,eAAe,MAAM;AAC5C,WAAO,UAAU;AACjB,aAAS,KAAK,OAAO,MAAM;AAAA,EAC7B,CAAC;AACH;AACA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAC7B,GAAG;AACD,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,eAAe,GAAG;AAChF,aAAW,eAAe,OAAO,iBAAiB,mBAAmB;AACvE;AACA,IAAI,gCAAgC;;;AD7TpC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAA;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["React"]}