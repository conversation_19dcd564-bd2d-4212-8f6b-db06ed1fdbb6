{"version": 3, "sources": ["../../vimeo-video-element/dist/react.js", "../../@vimeo/player/dist/player.es.js", "../../vimeo-video-element/dist/vimeo-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./vimeo-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"vimeo-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "/*! @vimeo/player v2.29.0 | (c) 2025 Vimeo | MIT License | https://github.com/vimeo/player.js */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function () {\n    return exports;\n  };\n  var exports = {},\n    Op = Object.prototype,\n    hasOwn = Op.hasOwnProperty,\n    defineProperty = Object.defineProperty || function (obj, key, desc) {\n      obj[key] = desc.value;\n    },\n    $Symbol = \"function\" == typeof Symbol ? Symbol : {},\n    iteratorSymbol = $Symbol.iterator || \"@@iterator\",\n    asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\",\n    toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function (obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator,\n      generator = Object.create(protoGenerator.prototype),\n      context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf,\n    NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function (method) {\n      define(prototype, method, function (arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg,\n          value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function (value) {\n          invoke(\"next\", value, resolve, reject);\n        }, function (err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function (unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function (error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function (method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function (resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function (method, arg) {\n      if (\"executing\" === state) throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method) throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg;;) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method) context.sent = context._sent = context.arg;else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state) throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel) continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method,\n      method = delegate.iterator[methodName];\n    if (undefined === method) return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = undefined, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type) return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next) return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1,\n          next = function next() {\n            for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next;\n            return next.value = undefined, next.done = !0, next;\n          };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: undefined,\n      done: !0\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function (genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function (genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function (arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function () {\n    return this;\n  }), define(Gp, \"toString\", function () {\n    return \"[object Generator]\";\n  }), exports.keys = function (val) {\n    var object = Object(val),\n      keys = [];\n    for (var key in object) keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length;) {\n        var key = keys.pop();\n        if (key in object) return next.value = key, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function (skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined);\n    },\n    stop: function () {\n      this.done = !0;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type) throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function (exception) {\n      if (this.done) throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = undefined), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i],\n          record = entry.completion;\n        if (\"root\" === entry.tryLoc) return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"),\n            hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n          } else {\n            if (!hasFinally) throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function (type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function (record, afterLoc) {\n      if (\"throw\" === record.type) throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function (finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function (tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function (iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      }, \"next\" === this.method && (this.arg = undefined), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n  return _wrapNativeSuper(Class);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\n/**\n * @module lib/functions\n */\n\n/**\n * Check to see this is a node environment.\n * @type {Boolean}\n */\n/* global global */\nvar isNode = typeof global !== 'undefined' && {}.toString.call(global) === '[object global]';\n\n/**\n * Get the name of the method for a given getter or setter.\n *\n * @param {string} prop The name of the property.\n * @param {string} type Either “get” or “set”.\n * @return {string}\n */\nfunction getMethodName(prop, type) {\n  if (prop.indexOf(type.toLowerCase()) === 0) {\n    return prop;\n  }\n  return \"\".concat(type.toLowerCase()).concat(prop.substr(0, 1).toUpperCase()).concat(prop.substr(1));\n}\n\n/**\n * Check to see if the object is a DOM Element.\n *\n * @param {*} element The object to check.\n * @return {boolean}\n */\nfunction isDomElement(element) {\n  return Boolean(element && element.nodeType === 1 && 'nodeName' in element && element.ownerDocument && element.ownerDocument.defaultView);\n}\n\n/**\n * Check to see whether the value is a number.\n *\n * @see http://dl.dropboxusercontent.com/u/35146/js/tests/isNumber.html\n * @param {*} value The value to check.\n * @param {boolean} integer Check if the value is an integer.\n * @return {boolean}\n */\nfunction isInteger(value) {\n  // eslint-disable-next-line eqeqeq\n  return !isNaN(parseFloat(value)) && isFinite(value) && Math.floor(value) == value;\n}\n\n/**\n * Check to see if the URL is a Vimeo url.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoUrl(url) {\n  return /^(https?:)?\\/\\/((((player|www)\\.)?vimeo\\.com)|((player\\.)?[a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))(?=$|\\/)/.test(url);\n}\n\n/**\n * Check to see if the URL is for a Vimeo embed.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoEmbed(url) {\n  var expr = /^https:\\/\\/player\\.((vimeo\\.com)|([a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))\\/video\\/\\d+/;\n  return expr.test(url);\n}\nfunction getOembedDomain(url) {\n  var match = (url || '').match(/^(?:https?:)?(?:\\/\\/)?([^/?]+)/);\n  var domain = (match && match[1] || '').replace('player.', '');\n  var customDomains = ['.videoji.hk', '.vimeo.work', '.videoji.cn'];\n  for (var _i = 0, _customDomains = customDomains; _i < _customDomains.length; _i++) {\n    var customDomain = _customDomains[_i];\n    if (domain.endsWith(customDomain)) {\n      return domain;\n    }\n  }\n  return 'vimeo.com';\n}\n\n/**\n * Get the Vimeo URL from an element.\n * The element must have either a data-vimeo-id or data-vimeo-url attribute.\n *\n * @param {object} oEmbedParameters The oEmbed parameters.\n * @return {string}\n */\nfunction getVimeoUrl() {\n  var oEmbedParameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var id = oEmbedParameters.id;\n  var url = oEmbedParameters.url;\n  var idOrUrl = id || url;\n  if (!idOrUrl) {\n    throw new Error('An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.');\n  }\n  if (isInteger(idOrUrl)) {\n    return \"https://vimeo.com/\".concat(idOrUrl);\n  }\n  if (isVimeoUrl(idOrUrl)) {\n    return idOrUrl.replace('http:', 'https:');\n  }\n  if (id) {\n    throw new TypeError(\"\\u201C\".concat(id, \"\\u201D is not a valid video id.\"));\n  }\n  throw new TypeError(\"\\u201C\".concat(idOrUrl, \"\\u201D is not a vimeo.com url.\"));\n}\n\n/* eslint-disable max-params */\n/**\n * A utility method for attaching and detaching event handlers\n *\n * @param {EventTarget} target\n * @param {string | string[]} eventName\n * @param {function} callback\n * @param {'addEventListener' | 'on'} onName\n * @param {'removeEventListener' | 'off'} offName\n * @return {{cancel: (function(): void)}}\n */\nvar subscribe = function subscribe(target, eventName, callback) {\n  var onName = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'addEventListener';\n  var offName = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'removeEventListener';\n  var eventNames = typeof eventName === 'string' ? [eventName] : eventName;\n  eventNames.forEach(function (evName) {\n    target[onName](evName, callback);\n  });\n  return {\n    cancel: function cancel() {\n      return eventNames.forEach(function (evName) {\n        return target[offName](evName, callback);\n      });\n    }\n  };\n};\n\nvar arrayIndexOfSupport = typeof Array.prototype.indexOf !== 'undefined';\nvar postMessageSupport = typeof window !== 'undefined' && typeof window.postMessage !== 'undefined';\nif (!isNode && (!arrayIndexOfSupport || !postMessageSupport)) {\n  throw new Error('Sorry, the Vimeo Player API is not available in this browser.');\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\n/*!\n * weakmap-polyfill v2.0.4 - ECMAScript6 WeakMap polyfill\n * https://github.com/polygonplanet/weakmap-polyfill\n * Copyright (c) 2015-2021 polygonplanet <<EMAIL>>\n * @license MIT\n */\n\n(function (self) {\n\n  if (self.WeakMap) {\n    return;\n  }\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  var hasDefine = Object.defineProperty && function () {\n    try {\n      // Avoid IE8's broken Object.defineProperty\n      return Object.defineProperty({}, 'x', {\n        value: 1\n      }).x === 1;\n    } catch (e) {}\n  }();\n  var defineProperty = function (object, name, value) {\n    if (hasDefine) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n  self.WeakMap = function () {\n    // ECMA-262 23.3 WeakMap Objects\n    function WeakMap() {\n      if (this === void 0) {\n        throw new TypeError(\"Constructor WeakMap requires 'new'\");\n      }\n      defineProperty(this, '_id', genId('_WeakMap'));\n\n      // ECMA-262 23.3.1.1 WeakMap([iterable])\n      if (arguments.length > 0) {\n        // Currently, WeakMap `iterable` argument is not supported\n        throw new TypeError('WeakMap iterable is not supported');\n      }\n    }\n\n    // ECMA-262 23.3.3.2 WeakMap.prototype.delete(key)\n    defineProperty(WeakMap.prototype, 'delete', function (key) {\n      checkInstance(this, 'delete');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        delete key[this._id];\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.3 WeakMap.prototype.get(key)\n    defineProperty(WeakMap.prototype, 'get', function (key) {\n      checkInstance(this, 'get');\n      if (!isObject(key)) {\n        return void 0;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return entry[1];\n      }\n      return void 0;\n    });\n\n    // ECMA-262 23.3.3.4 WeakMap.prototype.has(key)\n    defineProperty(WeakMap.prototype, 'has', function (key) {\n      checkInstance(this, 'has');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.5 WeakMap.prototype.set(key, value)\n    defineProperty(WeakMap.prototype, 'set', function (key, value) {\n      checkInstance(this, 'set');\n      if (!isObject(key)) {\n        throw new TypeError('Invalid value used as weak map key');\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        entry[1] = value;\n        return this;\n      }\n      defineProperty(key, this._id, [key, value]);\n      return this;\n    });\n    function checkInstance(x, methodName) {\n      if (!isObject(x) || !hasOwnProperty.call(x, '_id')) {\n        throw new TypeError(methodName + ' method called on incompatible receiver ' + typeof x);\n      }\n    }\n    function genId(prefix) {\n      return prefix + '_' + rand() + '.' + rand();\n    }\n    function rand() {\n      return Math.random().toString().substring(2);\n    }\n    defineProperty(WeakMap, '_polyfill', true);\n    return WeakMap;\n  }();\n  function isObject(x) {\n    return Object(x) === x;\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof commonjsGlobal !== 'undefined' ? commonjsGlobal : commonjsGlobal);\n\nvar npo_src = createCommonjsModule(function (module) {\n/*! Native Promise Only\n    v0.8.1 (c) Kyle Simpson\n    MIT License: http://getify.mit-license.org\n*/\n\n(function UMD(name, context, definition) {\n  // special form of UMD for polyfilling across evironments\n  context[name] = context[name] || definition();\n  if ( module.exports) {\n    module.exports = context[name];\n  }\n})(\"Promise\", typeof commonjsGlobal != \"undefined\" ? commonjsGlobal : commonjsGlobal, function DEF() {\n\n  var builtInProp,\n    cycle,\n    scheduling_queue,\n    ToString = Object.prototype.toString,\n    timer = typeof setImmediate != \"undefined\" ? function timer(fn) {\n      return setImmediate(fn);\n    } : setTimeout;\n\n  // dammit, IE8.\n  try {\n    Object.defineProperty({}, \"x\", {});\n    builtInProp = function builtInProp(obj, name, val, config) {\n      return Object.defineProperty(obj, name, {\n        value: val,\n        writable: true,\n        configurable: config !== false\n      });\n    };\n  } catch (err) {\n    builtInProp = function builtInProp(obj, name, val) {\n      obj[name] = val;\n      return obj;\n    };\n  }\n\n  // Note: using a queue instead of array for efficiency\n  scheduling_queue = function Queue() {\n    var first, last, item;\n    function Item(fn, self) {\n      this.fn = fn;\n      this.self = self;\n      this.next = void 0;\n    }\n    return {\n      add: function add(fn, self) {\n        item = new Item(fn, self);\n        if (last) {\n          last.next = item;\n        } else {\n          first = item;\n        }\n        last = item;\n        item = void 0;\n      },\n      drain: function drain() {\n        var f = first;\n        first = last = cycle = void 0;\n        while (f) {\n          f.fn.call(f.self);\n          f = f.next;\n        }\n      }\n    };\n  }();\n  function schedule(fn, self) {\n    scheduling_queue.add(fn, self);\n    if (!cycle) {\n      cycle = timer(scheduling_queue.drain);\n    }\n  }\n\n  // promise duck typing\n  function isThenable(o) {\n    var _then,\n      o_type = typeof o;\n    if (o != null && (o_type == \"object\" || o_type == \"function\")) {\n      _then = o.then;\n    }\n    return typeof _then == \"function\" ? _then : false;\n  }\n  function notify() {\n    for (var i = 0; i < this.chain.length; i++) {\n      notifyIsolated(this, this.state === 1 ? this.chain[i].success : this.chain[i].failure, this.chain[i]);\n    }\n    this.chain.length = 0;\n  }\n\n  // NOTE: This is a separate function to isolate\n  // the `try..catch` so that other code can be\n  // optimized better\n  function notifyIsolated(self, cb, chain) {\n    var ret, _then;\n    try {\n      if (cb === false) {\n        chain.reject(self.msg);\n      } else {\n        if (cb === true) {\n          ret = self.msg;\n        } else {\n          ret = cb.call(void 0, self.msg);\n        }\n        if (ret === chain.promise) {\n          chain.reject(TypeError(\"Promise-chain cycle\"));\n        } else if (_then = isThenable(ret)) {\n          _then.call(ret, chain.resolve, chain.reject);\n        } else {\n          chain.resolve(ret);\n        }\n      }\n    } catch (err) {\n      chain.reject(err);\n    }\n  }\n  function resolve(msg) {\n    var _then,\n      self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    try {\n      if (_then = isThenable(msg)) {\n        schedule(function () {\n          var def_wrapper = new MakeDefWrapper(self);\n          try {\n            _then.call(msg, function $resolve$() {\n              resolve.apply(def_wrapper, arguments);\n            }, function $reject$() {\n              reject.apply(def_wrapper, arguments);\n            });\n          } catch (err) {\n            reject.call(def_wrapper, err);\n          }\n        });\n      } else {\n        self.msg = msg;\n        self.state = 1;\n        if (self.chain.length > 0) {\n          schedule(notify, self);\n        }\n      }\n    } catch (err) {\n      reject.call(new MakeDefWrapper(self), err);\n    }\n  }\n  function reject(msg) {\n    var self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    self.msg = msg;\n    self.state = 2;\n    if (self.chain.length > 0) {\n      schedule(notify, self);\n    }\n  }\n  function iteratePromises(Constructor, arr, resolver, rejecter) {\n    for (var idx = 0; idx < arr.length; idx++) {\n      (function IIFE(idx) {\n        Constructor.resolve(arr[idx]).then(function $resolver$(msg) {\n          resolver(idx, msg);\n        }, rejecter);\n      })(idx);\n    }\n  }\n  function MakeDefWrapper(self) {\n    this.def = self;\n    this.triggered = false;\n  }\n  function MakeDef(self) {\n    this.promise = self;\n    this.state = 0;\n    this.triggered = false;\n    this.chain = [];\n    this.msg = void 0;\n  }\n  function Promise(executor) {\n    if (typeof executor != \"function\") {\n      throw TypeError(\"Not a function\");\n    }\n    if (this.__NPO__ !== 0) {\n      throw TypeError(\"Not a promise\");\n    }\n\n    // instance shadowing the inherited \"brand\"\n    // to signal an already \"initialized\" promise\n    this.__NPO__ = 1;\n    var def = new MakeDef(this);\n    this[\"then\"] = function then(success, failure) {\n      var o = {\n        success: typeof success == \"function\" ? success : true,\n        failure: typeof failure == \"function\" ? failure : false\n      };\n      // Note: `then(..)` itself can be borrowed to be used against\n      // a different promise constructor for making the chained promise,\n      // by substituting a different `this` binding.\n      o.promise = new this.constructor(function extractChain(resolve, reject) {\n        if (typeof resolve != \"function\" || typeof reject != \"function\") {\n          throw TypeError(\"Not a function\");\n        }\n        o.resolve = resolve;\n        o.reject = reject;\n      });\n      def.chain.push(o);\n      if (def.state !== 0) {\n        schedule(notify, def);\n      }\n      return o.promise;\n    };\n    this[\"catch\"] = function $catch$(failure) {\n      return this.then(void 0, failure);\n    };\n    try {\n      executor.call(void 0, function publicResolve(msg) {\n        resolve.call(def, msg);\n      }, function publicReject(msg) {\n        reject.call(def, msg);\n      });\n    } catch (err) {\n      reject.call(def, err);\n    }\n  }\n  var PromisePrototype = builtInProp({}, \"constructor\", Promise, /*configurable=*/false);\n\n  // Note: Android 4 cannot use `Object.defineProperty(..)` here\n  Promise.prototype = PromisePrototype;\n\n  // built-in \"brand\" to signal an \"uninitialized\" promise\n  builtInProp(PromisePrototype, \"__NPO__\", 0, /*configurable=*/false);\n  builtInProp(Promise, \"resolve\", function Promise$resolve(msg) {\n    var Constructor = this;\n\n    // spec mandated checks\n    // note: best \"isPromise\" check that's practical for now\n    if (msg && typeof msg == \"object\" && msg.__NPO__ === 1) {\n      return msg;\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      resolve(msg);\n    });\n  });\n  builtInProp(Promise, \"reject\", function Promise$reject(msg) {\n    return new this(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      reject(msg);\n    });\n  });\n  builtInProp(Promise, \"all\", function Promise$all(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    if (arr.length === 0) {\n      return Constructor.resolve([]);\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      var len = arr.length,\n        msgs = Array(len),\n        count = 0;\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        msgs[idx] = msg;\n        if (++count === len) {\n          resolve(msgs);\n        }\n      }, reject);\n    });\n  });\n  builtInProp(Promise, \"race\", function Promise$race(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        resolve(msg);\n      }, reject);\n    });\n  });\n  return Promise;\n});\n});\n\n/**\n * @module lib/callbacks\n */\n\nvar callbackMap = new WeakMap();\n\n/**\n * Store a callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @param {(function(this:Player, *): void|{resolve: function, reject: function})} callback\n *        The callback to call or an object with resolve and reject functions for a promise.\n * @return {void}\n */\nfunction storeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!(name in playerCallbacks)) {\n    playerCallbacks[name] = [];\n  }\n  playerCallbacks[name].push(callback);\n  callbackMap.set(player.element, playerCallbacks);\n}\n\n/**\n * Get the callbacks for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @return {function[]}\n */\nfunction getCallbacks(player, name) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  return playerCallbacks[name] || [];\n}\n\n/**\n * Remove a stored callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @param {function} [callback] The specific callback to remove.\n * @return {boolean} Was this the last callback?\n */\nfunction removeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!playerCallbacks[name]) {\n    return true;\n  }\n\n  // If no callback is passed, remove all callbacks for the event\n  if (!callback) {\n    playerCallbacks[name] = [];\n    callbackMap.set(player.element, playerCallbacks);\n    return true;\n  }\n  var index = playerCallbacks[name].indexOf(callback);\n  if (index !== -1) {\n    playerCallbacks[name].splice(index, 1);\n  }\n  callbackMap.set(player.element, playerCallbacks);\n  return playerCallbacks[name] && playerCallbacks[name].length === 0;\n}\n\n/**\n * Return the first stored callback for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @return {function} The callback, or false if there were none\n */\nfunction shiftCallbacks(player, name) {\n  var playerCallbacks = getCallbacks(player, name);\n  if (playerCallbacks.length < 1) {\n    return false;\n  }\n  var callback = playerCallbacks.shift();\n  removeCallback(player, name, callback);\n  return callback;\n}\n\n/**\n * Move callbacks associated with an element to another element.\n *\n * @param {HTMLElement} oldElement The old element.\n * @param {HTMLElement} newElement The new element.\n * @return {void}\n */\nfunction swapCallbacks(oldElement, newElement) {\n  var playerCallbacks = callbackMap.get(oldElement);\n  callbackMap.set(newElement, playerCallbacks);\n  callbackMap.delete(oldElement);\n}\n\n/**\n * @module lib/postmessage\n */\n\n/**\n * Parse a message received from postMessage.\n *\n * @param {*} data The data received from postMessage.\n * @return {object}\n */\nfunction parseMessageData(data) {\n  if (typeof data === 'string') {\n    try {\n      data = JSON.parse(data);\n    } catch (error) {\n      // If the message cannot be parsed, throw the error as a warning\n      console.warn(error);\n      return {};\n    }\n  }\n  return data;\n}\n\n/**\n * Post a message to the specified target.\n *\n * @param {Player} player The player object to use.\n * @param {string} method The API method to call.\n * @param {string|number|object|Array|undefined} params The parameters to send to the player.\n * @return {void}\n */\nfunction postMessage(player, method, params) {\n  if (!player.element.contentWindow || !player.element.contentWindow.postMessage) {\n    return;\n  }\n  var message = {\n    method: method\n  };\n  if (params !== undefined) {\n    message.value = params;\n  }\n\n  // IE 8 and 9 do not support passing messages, so stringify them\n  var ieVersion = parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\\d+).*$/, '$1'));\n  if (ieVersion >= 8 && ieVersion < 10) {\n    message = JSON.stringify(message);\n  }\n  player.element.contentWindow.postMessage(message, player.origin);\n}\n\n/**\n * Parse the data received from a message event.\n *\n * @param {Player} player The player that received the message.\n * @param {(Object|string)} data The message data. Strings will be parsed into JSON.\n * @return {void}\n */\nfunction processData(player, data) {\n  data = parseMessageData(data);\n  var callbacks = [];\n  var param;\n  if (data.event) {\n    if (data.event === 'error') {\n      var promises = getCallbacks(player, data.data.method);\n      promises.forEach(function (promise) {\n        var error = new Error(data.data.message);\n        error.name = data.data.name;\n        promise.reject(error);\n        removeCallback(player, data.data.method, promise);\n      });\n    }\n    callbacks = getCallbacks(player, \"event:\".concat(data.event));\n    param = data.data;\n  } else if (data.method) {\n    var callback = shiftCallbacks(player, data.method);\n    if (callback) {\n      callbacks.push(callback);\n      param = data.value;\n    }\n  }\n  callbacks.forEach(function (callback) {\n    try {\n      if (typeof callback === 'function') {\n        callback.call(player, param);\n        return;\n      }\n      callback.resolve(param);\n    } catch (e) {\n      // empty\n    }\n  });\n}\n\n/**\n * @module lib/embed\n */\nvar oEmbedParameters = ['airplay', 'audio_tracks', 'audiotrack', 'autopause', 'autoplay', 'background', 'byline', 'cc', 'chapter_id', 'chapters', 'chromecast', 'color', 'colors', 'controls', 'dnt', 'end_time', 'fullscreen', 'height', 'id', 'initial_quality', 'interactive_params', 'keyboard', 'loop', 'maxheight', 'max_quality', 'maxwidth', 'min_quality', 'muted', 'play_button_position', 'playsinline', 'portrait', 'preload', 'progress_bar', 'quality', 'quality_selector', 'responsive', 'skipping_forward', 'speed', 'start_time', 'texttrack', 'thumbnail_id', 'title', 'transcript', 'transparent', 'unmute_button', 'url', 'vimeo_logo', 'volume', 'watch_full_video', 'width'];\n\n/**\n * Get the 'data-vimeo'-prefixed attributes from an element as an object.\n *\n * @param {HTMLElement} element The element.\n * @param {Object} [defaults={}] The default values to use.\n * @return {Object<string, string>}\n */\nfunction getOEmbedParameters(element) {\n  var defaults = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return oEmbedParameters.reduce(function (params, param) {\n    var value = element.getAttribute(\"data-vimeo-\".concat(param));\n    if (value || value === '') {\n      params[param] = value === '' ? 1 : value;\n    }\n    return params;\n  }, defaults);\n}\n\n/**\n * Create an embed from oEmbed data inside an element.\n *\n * @param {object} data The oEmbed data.\n * @param {HTMLElement} element The element to put the iframe in.\n * @return {HTMLIFrameElement} The iframe embed.\n */\nfunction createEmbed(_ref, element) {\n  var html = _ref.html;\n  if (!element) {\n    throw new TypeError('An element must be provided');\n  }\n  if (element.getAttribute('data-vimeo-initialized') !== null) {\n    return element.querySelector('iframe');\n  }\n  var div = document.createElement('div');\n  div.innerHTML = html;\n  element.appendChild(div.firstChild);\n  element.setAttribute('data-vimeo-initialized', 'true');\n  return element.querySelector('iframe');\n}\n\n/**\n * Make an oEmbed call for the specified URL.\n *\n * @param {string} videoUrl The vimeo.com url for the video.\n * @param {Object} [params] Parameters to pass to oEmbed.\n * @param {HTMLElement} element The element.\n * @return {Promise}\n */\nfunction getOEmbedData(videoUrl) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var element = arguments.length > 2 ? arguments[2] : undefined;\n  return new Promise(function (resolve, reject) {\n    if (!isVimeoUrl(videoUrl)) {\n      throw new TypeError(\"\\u201C\".concat(videoUrl, \"\\u201D is not a vimeo.com url.\"));\n    }\n    var domain = getOembedDomain(videoUrl);\n    var url = \"https://\".concat(domain, \"/api/oembed.json?url=\").concat(encodeURIComponent(videoUrl));\n    for (var param in params) {\n      if (params.hasOwnProperty(param)) {\n        url += \"&\".concat(param, \"=\").concat(encodeURIComponent(params[param]));\n      }\n    }\n    var xhr = 'XDomainRequest' in window ? new XDomainRequest() : new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.onload = function () {\n      if (xhr.status === 404) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D was not found.\")));\n        return;\n      }\n      if (xhr.status === 403) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n        return;\n      }\n      try {\n        var json = JSON.parse(xhr.responseText);\n        // Check api response for 403 on oembed\n        if (json.domain_status_code === 403) {\n          // We still want to create the embed to give users visual feedback\n          createEmbed(json, element);\n          reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n          return;\n        }\n        resolve(json);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    xhr.onerror = function () {\n      var status = xhr.status ? \" (\".concat(xhr.status, \")\") : '';\n      reject(new Error(\"There was an error fetching the embed code from Vimeo\".concat(status, \".\")));\n    };\n    xhr.send();\n  });\n}\n\n/**\n * Initialize all embeds within a specific element\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initializeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  var elements = [].slice.call(parent.querySelectorAll('[data-vimeo-id], [data-vimeo-url]'));\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error creating an embed: \".concat(error));\n    }\n  };\n  elements.forEach(function (element) {\n    try {\n      // Skip any that have data-vimeo-defer\n      if (element.getAttribute('data-vimeo-defer') !== null) {\n        return;\n      }\n      var params = getOEmbedParameters(element);\n      var url = getVimeoUrl(params);\n      getOEmbedData(url, params, element).then(function (data) {\n        return createEmbed(data, element);\n      }).catch(handleError);\n    } catch (error) {\n      handleError(error);\n    }\n  });\n}\n\n/**\n * Resize embeds when messaged by the player.\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction resizeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  // Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoPlayerResizeEmbeds_) {\n    return;\n  }\n  window.VimeoPlayerResizeEmbeds_ = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n\n    // 'spacechange' is fired only on embeds with cards\n    if (!event.data || event.data.event !== 'spacechange') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      if (iframes[i].contentWindow !== event.source) {\n        continue;\n      }\n\n      // Change padding-bottom of the enclosing div to accommodate\n      // card carousel without distorting aspect ratio\n      var space = iframes[i].parentElement;\n      space.style.paddingBottom = \"\".concat(event.data.data[0].bottom, \"px\");\n      break;\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Add chapters to existing metadata for Google SEO\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initAppendVideoMetadata() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoSeoMetadataAppended) {\n    return;\n  }\n  window.VimeoSeoMetadataAppended = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      var iframe = iframes[i];\n\n      // Initiate appendVideoMetadata if iframe is a Vimeo embed\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.callMethod('appendVideoMetadata', window.location.href);\n      }\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Seek to time indicated by vimeo_t query parameter if present in URL\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction checkUrlTimeParam() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoCheckedUrlTimeParam) {\n    return;\n  }\n  window.VimeoCheckedUrlTimeParam = true;\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error getting video Id: \".concat(error));\n    }\n  };\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    var _loop = function _loop() {\n      var iframe = iframes[i];\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.getVideoId().then(function (videoId) {\n          var matches = new RegExp(\"[?&]vimeo_t_\".concat(videoId, \"=([^&#]*)\")).exec(window.location.href);\n          if (matches && matches[1]) {\n            var sec = decodeURI(matches[1]);\n            player.setCurrentTime(sec);\n          }\n          return;\n        }).catch(handleError);\n      }\n    };\n    for (var i = 0; i < iframes.length; i++) {\n      _loop();\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/* MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nTerms */\n\nfunction initializeScreenfull() {\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n    // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n    // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n    for (; i < l; i++) {\n      val = fnMap[i];\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n        return ret;\n      }\n    }\n    return false;\n  }();\n  var eventNameMap = {\n    fullscreenchange: fn.fullscreenchange,\n    fullscreenerror: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function request(element) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function onFullScreenEntered() {\n          screenfull.off('fullscreenchange', onFullScreenEntered);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      });\n    },\n    exit: function exit() {\n      return new Promise(function (resolve, reject) {\n        if (!screenfull.isFullscreen) {\n          resolve();\n          return;\n        }\n        var onFullScreenExit = function onFullScreenExit() {\n          screenfull.off('fullscreenchange', onFullScreenExit);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      });\n    },\n    on: function on(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.addEventListener(eventName, callback);\n      }\n    },\n    off: function off(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.removeEventListener(eventName, callback);\n      }\n    }\n  };\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function get() {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function get() {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function get() {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n  return screenfull;\n}\n\n/** @typedef {import('./timing-src-connector.types').PlayerControls} PlayerControls */\n/** @typedef {import('./timing-object.types').TimingObject} TimingObject */\n/** @typedef {import('./timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n/** @typedef {(msg: string) => any} Logger */\n/** @typedef {import('timing-object.types').TConnectionState} TConnectionState */\n\n/**\n * @type {TimingSrcConnectorOptions}\n *\n * For details on these properties and their effects, see the typescript definition referenced above.\n */\nvar defaultOptions = {\n  role: 'viewer',\n  autoPlayMuted: true,\n  allowedDrift: 0.3,\n  maxAllowedDrift: 1,\n  minCheckInterval: 0.1,\n  maxRateAdjustment: 0.2,\n  maxTimeToCatchUp: 1\n};\n\n/**\n * There's a proposed W3C spec for the Timing Object which would introduce a new set of APIs that would simplify time-synchronization tasks for browser applications.\n *\n * Proposed spec: https://webtiming.github.io/timingobject/\n * V3 Spec: https://timingsrc.readthedocs.io/en/latest/\n * Demuxed talk: https://www.youtube.com/watch?v=cZSjDaGDmX8\n *\n * This class makes it easy to connect Vimeo.Player to a provided TimingObject via Vimeo.Player.setTimingSrc(myTimingObject, options) and the synchronization will be handled automatically.\n *\n * There are 5 general responsibilities in TimingSrcConnector:\n *\n * 1. `updatePlayer()` which sets the player's currentTime, playbackRate and pause/play state based on current state of the TimingObject.\n * 2. `updateTimingObject()` which sets the TimingObject's position and velocity from the player's state.\n * 3. `playerUpdater` which listens for change events on the TimingObject and will respond by calling updatePlayer.\n * 4. `timingObjectUpdater` which listens to the player events of seeked, play and pause and will respond by calling `updateTimingObject()`.\n * 5. `maintainPlaybackPosition` this is code that constantly monitors the player to make sure it's always in sync with the TimingObject. This is needed because videos will generally not play with precise time accuracy and there will be some drift which becomes more noticeable over longer periods (as noted in the timing-object spec). More details on this method below.\n */\nvar TimingSrcConnector = /*#__PURE__*/function (_EventTarget) {\n  _inherits(TimingSrcConnector, _EventTarget);\n  var _super = _createSuper(TimingSrcConnector);\n  /**\n   * @param {PlayerControls} player\n   * @param {TimingObject} timingObject\n   * @param {TimingSrcConnectorOptions} options\n   * @param {Logger} logger\n   */\n  function TimingSrcConnector(_player, timingObject) {\n    var _this;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var logger = arguments.length > 3 ? arguments[3] : undefined;\n    _classCallCheck(this, TimingSrcConnector);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"logger\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"speedAdjustment\", 0);\n    /**\n     * @param {PlayerControls} player\n     * @param {number} newAdjustment\n     * @return {Promise<void>}\n     */\n    _defineProperty(_assertThisInitialized(_this), \"adjustSpeed\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(player, newAdjustment) {\n        var newPlaybackRate;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!(_this.speedAdjustment === newAdjustment)) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 2:\n              _context.next = 4;\n              return player.getPlaybackRate();\n            case 4:\n              _context.t0 = _context.sent;\n              _context.t1 = _this.speedAdjustment;\n              _context.t2 = _context.t0 - _context.t1;\n              _context.t3 = newAdjustment;\n              newPlaybackRate = _context.t2 + _context.t3;\n              _this.log(\"New playbackRate:  \".concat(newPlaybackRate));\n              _context.next = 12;\n              return player.setPlaybackRate(newPlaybackRate);\n            case 12:\n              _this.speedAdjustment = newAdjustment;\n            case 13:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _this.logger = logger;\n    _this.init(timingObject, _player, _objectSpread2(_objectSpread2({}, defaultOptions), options));\n    return _this;\n  }\n  _createClass(TimingSrcConnector, [{\n    key: \"disconnect\",\n    value: function disconnect() {\n      this.dispatchEvent(new Event('disconnect'));\n    }\n\n    /**\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"init\",\n    value: function () {\n      var _init = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(timingObject, player, options) {\n        var _this2 = this;\n        var playerUpdater, positionSync, timingObjectUpdater;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return this.waitForTOReadyState(timingObject, 'open');\n            case 2:\n              if (!(options.role === 'viewer')) {\n                _context2.next = 10;\n                break;\n              }\n              _context2.next = 5;\n              return this.updatePlayer(timingObject, player, options);\n            case 5:\n              playerUpdater = subscribe(timingObject, 'change', function () {\n                return _this2.updatePlayer(timingObject, player, options);\n              });\n              positionSync = this.maintainPlaybackPosition(timingObject, player, options);\n              this.addEventListener('disconnect', function () {\n                positionSync.cancel();\n                playerUpdater.cancel();\n              });\n              _context2.next = 14;\n              break;\n            case 10:\n              _context2.next = 12;\n              return this.updateTimingObject(timingObject, player);\n            case 12:\n              timingObjectUpdater = subscribe(player, ['seeked', 'play', 'pause', 'ratechange'], function () {\n                return _this2.updateTimingObject(timingObject, player);\n              }, 'on', 'off');\n              this.addEventListener('disconnect', function () {\n                return timingObjectUpdater.cancel();\n              });\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function init(_x3, _x4, _x5) {\n        return _init.apply(this, arguments);\n      }\n      return init;\n    }()\n    /**\n     * Sets the TimingObject's state to reflect that of the player\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updateTimingObject\",\n    value: function () {\n      var _updateTimingObject = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(timingObject, player) {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.t0 = timingObject;\n              _context3.next = 3;\n              return player.getCurrentTime();\n            case 3:\n              _context3.t1 = _context3.sent;\n              _context3.next = 6;\n              return player.getPaused();\n            case 6:\n              if (!_context3.sent) {\n                _context3.next = 10;\n                break;\n              }\n              _context3.t2 = 0;\n              _context3.next = 13;\n              break;\n            case 10:\n              _context3.next = 12;\n              return player.getPlaybackRate();\n            case 12:\n              _context3.t2 = _context3.sent;\n            case 13:\n              _context3.t3 = _context3.t2;\n              _context3.t4 = {\n                position: _context3.t1,\n                velocity: _context3.t3\n              };\n              _context3.t0.update.call(_context3.t0, _context3.t4);\n            case 16:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      function updateTimingObject(_x6, _x7) {\n        return _updateTimingObject.apply(this, arguments);\n      }\n      return updateTimingObject;\n    }()\n    /**\n     * Sets the player's timing state to reflect that of the TimingObject\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updatePlayer\",\n    value: function () {\n      var _updatePlayer = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(timingObject, player, options) {\n        var _timingObject$query, position, velocity;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _timingObject$query = timingObject.query(), position = _timingObject$query.position, velocity = _timingObject$query.velocity;\n              if (typeof position === 'number') {\n                player.setCurrentTime(position);\n              }\n              if (!(typeof velocity === 'number')) {\n                _context5.next = 25;\n                break;\n              }\n              if (!(velocity === 0)) {\n                _context5.next = 11;\n                break;\n              }\n              _context5.next = 6;\n              return player.getPaused();\n            case 6:\n              _context5.t0 = _context5.sent;\n              if (!(_context5.t0 === false)) {\n                _context5.next = 9;\n                break;\n              }\n              player.pause();\n            case 9:\n              _context5.next = 25;\n              break;\n            case 11:\n              if (!(velocity > 0)) {\n                _context5.next = 25;\n                break;\n              }\n              _context5.next = 14;\n              return player.getPaused();\n            case 14:\n              _context5.t1 = _context5.sent;\n              if (!(_context5.t1 === true)) {\n                _context5.next = 19;\n                break;\n              }\n              _context5.next = 18;\n              return player.play().catch( /*#__PURE__*/function () {\n                var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(err) {\n                  return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n                    while (1) switch (_context4.prev = _context4.next) {\n                      case 0:\n                        if (!(err.name === 'NotAllowedError' && options.autoPlayMuted)) {\n                          _context4.next = 5;\n                          break;\n                        }\n                        _context4.next = 3;\n                        return player.setMuted(true);\n                      case 3:\n                        _context4.next = 5;\n                        return player.play().catch(function (err2) {\n                          return console.error('Couldn\\'t play the video from TimingSrcConnector. Error:', err2);\n                        });\n                      case 5:\n                      case \"end\":\n                        return _context4.stop();\n                    }\n                  }, _callee4);\n                }));\n                return function (_x11) {\n                  return _ref2.apply(this, arguments);\n                };\n              }());\n            case 18:\n              this.updatePlayer(timingObject, player, options);\n            case 19:\n              _context5.next = 21;\n              return player.getPlaybackRate();\n            case 21:\n              _context5.t2 = _context5.sent;\n              _context5.t3 = velocity;\n              if (!(_context5.t2 !== _context5.t3)) {\n                _context5.next = 25;\n                break;\n              }\n              player.setPlaybackRate(velocity);\n            case 25:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, this);\n      }));\n      function updatePlayer(_x8, _x9, _x10) {\n        return _updatePlayer.apply(this, arguments);\n      }\n      return updatePlayer;\n    }()\n    /**\n     * Since video players do not play with 100% time precision, we need to closely monitor\n     * our player to be sure it remains in sync with the TimingObject.\n     *\n     * If out of sync, we use the current conditions and the options provided to determine\n     * whether to re-sync via setting currentTime or adjusting the playbackRate\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {{cancel: (function(): void)}}\n     */\n  }, {\n    key: \"maintainPlaybackPosition\",\n    value: function maintainPlaybackPosition(timingObject, player, options) {\n      var _this3 = this;\n      var allowedDrift = options.allowedDrift,\n        maxAllowedDrift = options.maxAllowedDrift,\n        minCheckInterval = options.minCheckInterval,\n        maxRateAdjustment = options.maxRateAdjustment,\n        maxTimeToCatchUp = options.maxTimeToCatchUp;\n      var syncInterval = Math.min(maxTimeToCatchUp, Math.max(minCheckInterval, maxAllowedDrift)) * 1000;\n      var check = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n          var diff, diffAbs, min, max, adjustment;\n          return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n            while (1) switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.t0 = timingObject.query().velocity === 0;\n                if (_context6.t0) {\n                  _context6.next = 6;\n                  break;\n                }\n                _context6.next = 4;\n                return player.getPaused();\n              case 4:\n                _context6.t1 = _context6.sent;\n                _context6.t0 = _context6.t1 === true;\n              case 6:\n                if (!_context6.t0) {\n                  _context6.next = 8;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 8:\n                _context6.t2 = timingObject.query().position;\n                _context6.next = 11;\n                return player.getCurrentTime();\n              case 11:\n                _context6.t3 = _context6.sent;\n                diff = _context6.t2 - _context6.t3;\n                diffAbs = Math.abs(diff);\n                _this3.log(\"Drift: \".concat(diff));\n                if (!(diffAbs > maxAllowedDrift)) {\n                  _context6.next = 22;\n                  break;\n                }\n                _context6.next = 18;\n                return _this3.adjustSpeed(player, 0);\n              case 18:\n                player.setCurrentTime(timingObject.query().position);\n                _this3.log('Resync by currentTime');\n                _context6.next = 29;\n                break;\n              case 22:\n                if (!(diffAbs > allowedDrift)) {\n                  _context6.next = 29;\n                  break;\n                }\n                min = diffAbs / maxTimeToCatchUp;\n                max = maxRateAdjustment;\n                adjustment = min < max ? (max - min) / 2 : max;\n                _context6.next = 28;\n                return _this3.adjustSpeed(player, adjustment * Math.sign(diff));\n              case 28:\n                _this3.log('Resync by playbackRate');\n              case 29:\n              case \"end\":\n                return _context6.stop();\n            }\n          }, _callee6);\n        }));\n        return function check() {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      var interval = setInterval(function () {\n        return check();\n      }, syncInterval);\n      return {\n        cancel: function cancel() {\n          return clearInterval(interval);\n        }\n      };\n    }\n\n    /**\n     * @param {string} msg\n     */\n  }, {\n    key: \"log\",\n    value: function log(msg) {\n      var _this$logger;\n      (_this$logger = this.logger) === null || _this$logger === void 0 ? void 0 : _this$logger.call(this, \"TimingSrcConnector: \".concat(msg));\n    }\n  }, {\n    key: \"waitForTOReadyState\",\n    value:\n    /**\n     * @param {TimingObject} timingObject\n     * @param {TConnectionState} state\n     * @return {Promise<void>}\n     */\n    function waitForTOReadyState(timingObject, state) {\n      return new Promise(function (resolve) {\n        var check = function check() {\n          if (timingObject.readyState === state) {\n            resolve();\n          } else {\n            timingObject.addEventListener('readystatechange', check, {\n              once: true\n            });\n          }\n        };\n        check();\n      });\n    }\n  }]);\n  return TimingSrcConnector;\n}( /*#__PURE__*/_wrapNativeSuper(EventTarget));\n\nvar playerMap = new WeakMap();\nvar readyMap = new WeakMap();\nvar screenfull = {};\nvar Player = /*#__PURE__*/function () {\n  /**\n   * Create a Player.\n   *\n   * @param {(HTMLIFrameElement|HTMLElement|string|jQuery)} element A reference to the Vimeo\n   *        player iframe, and id, or a jQuery object.\n   * @param {object} [options] oEmbed parameters to use when creating an embed in the element.\n   * @return {Player}\n   */\n  function Player(element) {\n    var _this = this;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Player);\n    /* global jQuery */\n    if (window.jQuery && element instanceof jQuery) {\n      if (element.length > 1 && window.console && console.warn) {\n        console.warn('A jQuery object with multiple elements was passed, using the first element.');\n      }\n      element = element[0];\n    }\n\n    // Find an element by ID\n    if (typeof document !== 'undefined' && typeof element === 'string') {\n      element = document.getElementById(element);\n    }\n\n    // Not an element!\n    if (!isDomElement(element)) {\n      throw new TypeError('You must pass either a valid element or a valid id.');\n    }\n\n    // Already initialized an embed in this div, so grab the iframe\n    if (element.nodeName !== 'IFRAME') {\n      var iframe = element.querySelector('iframe');\n      if (iframe) {\n        element = iframe;\n      }\n    }\n\n    // iframe url is not a Vimeo url\n    if (element.nodeName === 'IFRAME' && !isVimeoUrl(element.getAttribute('src') || '')) {\n      throw new Error('The player element passed isn’t a Vimeo embed.');\n    }\n\n    // If there is already a player object in the map, return that\n    if (playerMap.has(element)) {\n      return playerMap.get(element);\n    }\n    this._window = element.ownerDocument.defaultView;\n    this.element = element;\n    this.origin = '*';\n    var readyPromise = new npo_src(function (resolve, reject) {\n      _this._onMessage = function (event) {\n        if (!isVimeoUrl(event.origin) || _this.element.contentWindow !== event.source) {\n          return;\n        }\n        if (_this.origin === '*') {\n          _this.origin = event.origin;\n        }\n        var data = parseMessageData(event.data);\n        var isError = data && data.event === 'error';\n        var isReadyError = isError && data.data && data.data.method === 'ready';\n        if (isReadyError) {\n          var error = new Error(data.data.message);\n          error.name = data.data.name;\n          reject(error);\n          return;\n        }\n        var isReadyEvent = data && data.event === 'ready';\n        var isPingResponse = data && data.method === 'ping';\n        if (isReadyEvent || isPingResponse) {\n          _this.element.setAttribute('data-ready', 'true');\n          resolve();\n          return;\n        }\n        processData(_this, data);\n      };\n      _this._window.addEventListener('message', _this._onMessage);\n      if (_this.element.nodeName !== 'IFRAME') {\n        var params = getOEmbedParameters(element, options);\n        var url = getVimeoUrl(params);\n        getOEmbedData(url, params, element).then(function (data) {\n          var iframe = createEmbed(data, element);\n          // Overwrite element with the new iframe,\n          // but store reference to the original element\n          _this.element = iframe;\n          _this._originalElement = element;\n          swapCallbacks(element, iframe);\n          playerMap.set(_this.element, _this);\n          return data;\n        }).catch(reject);\n      }\n    });\n\n    // Store a copy of this Player in the map\n    readyMap.set(this, readyPromise);\n    playerMap.set(this.element, this);\n\n    // Send a ping to the iframe so the ready promise will be resolved if\n    // the player is already ready.\n    if (this.element.nodeName === 'IFRAME') {\n      postMessage(this, 'ping');\n    }\n    if (screenfull.isEnabled) {\n      var exitFullscreen = function exitFullscreen() {\n        return screenfull.exit();\n      };\n      this.fullscreenchangeHandler = function () {\n        if (screenfull.isFullscreen) {\n          storeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        } else {\n          removeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        }\n        // eslint-disable-next-line\n        _this.ready().then(function () {\n          postMessage(_this, 'fullscreenchange', screenfull.isFullscreen);\n        });\n      };\n      screenfull.on('fullscreenchange', this.fullscreenchangeHandler);\n    }\n    return this;\n  }\n\n  /**\n   * Get a promise for a method.\n   *\n   * @param {string} name The API method to call.\n   * @param {...(string|number|object|Array)} args Arguments to send via postMessage.\n   * @return {Promise}\n   */\n  _createClass(Player, [{\n    key: \"callMethod\",\n    value: function callMethod(name) {\n      var _this2 = this;\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (name === undefined || name === null) {\n        throw new TypeError('You must pass a method name.');\n      }\n      return new npo_src(function (resolve, reject) {\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this2.ready().then(function () {\n          storeCallback(_this2, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this2, name, args);\n        }).catch(reject);\n      });\n    }\n    /**\n     * Get a promise for the value of a player property.\n     *\n     * @param {string} name The property name\n     * @return {Promise}\n     */\n  }, {\n    key: \"get\",\n    value: function get(name) {\n      var _this3 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'get');\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this3.ready().then(function () {\n          storeCallback(_this3, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this3, name);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Get a promise for setting the value of a player property.\n     *\n     * @param {string} name The API method to call.\n     * @param {mixed} value The value to set.\n     * @return {Promise}\n     */\n  }, {\n    key: \"set\",\n    value: function set(name, value) {\n      var _this4 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'set');\n        if (value === undefined || value === null) {\n          throw new TypeError('There must be a value to set.');\n        }\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this4.ready().then(function () {\n          storeCallback(_this4, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this4, name, value);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Add an event listener for the specified event. Will call the\n     * callback with a single parameter, `data`, that contains the data for\n     * that event.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function(*)} callback The function to call when the event fires.\n     * @return {void}\n     */\n  }, {\n    key: \"on\",\n    value: function on(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (!callback) {\n        throw new TypeError('You must pass a callback function.');\n      }\n      if (typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var callbacks = getCallbacks(this, \"event:\".concat(eventName));\n      if (callbacks.length === 0) {\n        this.callMethod('addEventListener', eventName).catch(function () {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n      storeCallback(this, \"event:\".concat(eventName), callback);\n    }\n\n    /**\n     * Remove an event listener for the specified event. Will remove all\n     * listeners for that event if a `callback` isn’t passed, or only that\n     * specific callback if it is passed.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function} [callback] The specific callback to remove.\n     * @return {void}\n     */\n  }, {\n    key: \"off\",\n    value: function off(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (callback && typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var lastCallback = removeCallback(this, \"event:\".concat(eventName), callback);\n\n      // If there are no callbacks left, remove the listener\n      if (lastCallback) {\n        this.callMethod('removeEventListener', eventName).catch(function (e) {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n    }\n\n    /**\n     * A promise to load a new video.\n     *\n     * @promise LoadVideoPromise\n     * @fulfill {number} The video with this id or url successfully loaded.\n     * @reject {TypeError} The id was not a number.\n     */\n    /**\n     * Load a new video into this embed. The promise will be resolved if\n     * the video is successfully loaded, or it will be rejected if it could\n     * not be loaded.\n     *\n     * @param {number|string|object} options The id of the video, the url of the video, or an object with embed options.\n     * @return {LoadVideoPromise}\n     */\n  }, {\n    key: \"loadVideo\",\n    value: function loadVideo(options) {\n      return this.callMethod('loadVideo', options);\n    }\n\n    /**\n     * A promise to perform an action when the Player is ready.\n     *\n     * @todo document errors\n     * @promise LoadVideoPromise\n     * @fulfill {void}\n     */\n    /**\n     * Trigger a function when the player iframe has initialized. You do not\n     * need to wait for `ready` to trigger to begin adding event listeners\n     * or calling other methods.\n     *\n     * @return {ReadyPromise}\n     */\n  }, {\n    key: \"ready\",\n    value: function ready() {\n      var readyPromise = readyMap.get(this) || new npo_src(function (resolve, reject) {\n        reject(new Error('Unknown player. Probably unloaded.'));\n      });\n      return npo_src.resolve(readyPromise);\n    }\n\n    /**\n     * A promise to add a cue point to the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point to use for removeCuePoint.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Add a cue point to the player.\n     *\n     * @param {number} time The time for the cue point.\n     * @param {object} [data] Arbitrary data to be returned with the cue point.\n     * @return {AddCuePointPromise}\n     */\n  }, {\n    key: \"addCuePoint\",\n    value: function addCuePoint(time) {\n      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return this.callMethod('addCuePoint', {\n        time: time,\n        data: data\n      });\n    }\n\n    /**\n     * A promise to remove a cue point from the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point that was removed.\n     * @reject {InvalidCuePoint} The cue point with the specified id was not\n     *         found.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Remove a cue point from the video.\n     *\n     * @param {string} id The id of the cue point to remove.\n     * @return {RemoveCuePointPromise}\n     */\n  }, {\n    key: \"removeCuePoint\",\n    value: function removeCuePoint(id) {\n      return this.callMethod('removeCuePoint', id);\n    }\n\n    /**\n     * A representation of a text track on a video.\n     *\n     * @typedef {Object} VimeoTextTrack\n     * @property {string} language The ISO language code.\n     * @property {string} kind The kind of track it is (captions or subtitles).\n     * @property {string} label The human‐readable label for the track.\n     */\n    /**\n     * A promise to enable a text track.\n     *\n     * @promise EnableTextTrackPromise\n     * @fulfill {VimeoTextTrack} The text track that was enabled.\n     * @reject {InvalidTrackLanguageError} No track was available with the\n     *         specified language.\n     * @reject {InvalidTrackError} No track was available with the specified\n     *         language and kind.\n     */\n    /**\n     * Enable the text track with the specified language, and optionally the\n     * specified kind (captions or subtitles).\n     *\n     * When set via the API, the track language will not change the viewer’s\n     * stored preference.\n     *\n     * @param {string} language The two‐letter language code.\n     * @param {string} [kind] The kind of track to enable (captions or subtitles).\n     * @return {EnableTextTrackPromise}\n     */\n  }, {\n    key: \"enableTextTrack\",\n    value: function enableTextTrack(language, kind) {\n      if (!language) {\n        throw new TypeError('You must pass a language.');\n      }\n      return this.callMethod('enableTextTrack', {\n        language: language,\n        kind: kind\n      });\n    }\n\n    /**\n     * A promise to disable the active text track.\n     *\n     * @promise DisableTextTrackPromise\n     * @fulfill {void} The track was disabled.\n     */\n    /**\n     * Disable the currently-active text track.\n     *\n     * @return {DisableTextTrackPromise}\n     */\n  }, {\n    key: \"disableTextTrack\",\n    value: function disableTextTrack() {\n      return this.callMethod('disableTextTrack');\n    }\n\n    /**\n     * A promise to pause the video.\n     *\n     * @promise PausePromise\n     * @fulfill {void} The video was paused.\n     */\n    /**\n     * Pause the video if it’s playing.\n     *\n     * @return {PausePromise}\n     */\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      return this.callMethod('pause');\n    }\n\n    /**\n     * A promise to play the video.\n     *\n     * @promise PlayPromise\n     * @fulfill {void} The video was played.\n     */\n    /**\n     * Play the video if it’s paused. **Note:** on iOS and some other\n     * mobile devices, you cannot programmatically trigger play. Once the\n     * viewer has tapped on the play button in the player, however, you\n     * will be able to use this function.\n     *\n     * @return {PlayPromise}\n     */\n  }, {\n    key: \"play\",\n    value: function play() {\n      return this.callMethod('play');\n    }\n\n    /**\n     * Request that the player enters fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestFullscreen\",\n    value: function requestFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.request(this.element);\n      }\n      return this.callMethod('requestFullscreen');\n    }\n\n    /**\n     * Request that the player exits fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitFullscreen\",\n    value: function exitFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.exit();\n      }\n      return this.callMethod('exitFullscreen');\n    }\n\n    /**\n     * Returns true if the player is currently fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getFullscreen\",\n    value: function getFullscreen() {\n      if (screenfull.isEnabled) {\n        return npo_src.resolve(screenfull.isFullscreen);\n      }\n      return this.get('fullscreen');\n    }\n\n    /**\n     * Request that the player enters picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestPictureInPicture\",\n    value: function requestPictureInPicture() {\n      return this.callMethod('requestPictureInPicture');\n    }\n\n    /**\n     * Request that the player exits picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitPictureInPicture\",\n    value: function exitPictureInPicture() {\n      return this.callMethod('exitPictureInPicture');\n    }\n\n    /**\n     * Returns true if the player is currently picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getPictureInPicture\",\n    value: function getPictureInPicture() {\n      return this.get('pictureInPicture');\n    }\n\n    /**\n     * A promise to prompt the viewer to initiate remote playback.\n     *\n     * @promise RemotePlaybackPromptPromise\n     * @fulfill {void}\n     * @reject {NotFoundError} No remote playback device is available.\n     */\n    /**\n     * Request to prompt the user to initiate remote playback.\n     *\n     * @return {RemotePlaybackPromptPromise}\n     */\n  }, {\n    key: \"remotePlaybackPrompt\",\n    value: function remotePlaybackPrompt() {\n      return this.callMethod('remotePlaybackPrompt');\n    }\n\n    /**\n     * A promise to unload the video.\n     *\n     * @promise UnloadPromise\n     * @fulfill {void} The video was unloaded.\n     */\n    /**\n     * Return the player to its initial state.\n     *\n     * @return {UnloadPromise}\n     */\n  }, {\n    key: \"unload\",\n    value: function unload() {\n      return this.callMethod('unload');\n    }\n\n    /**\n     * Cleanup the player and remove it from the DOM\n     *\n     * It won't be usable and a new one should be constructed\n     *  in order to do any operations.\n     *\n     * @return {Promise}\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this5 = this;\n      return new npo_src(function (resolve) {\n        readyMap.delete(_this5);\n        playerMap.delete(_this5.element);\n        if (_this5._originalElement) {\n          playerMap.delete(_this5._originalElement);\n          _this5._originalElement.removeAttribute('data-vimeo-initialized');\n        }\n        if (_this5.element && _this5.element.nodeName === 'IFRAME' && _this5.element.parentNode) {\n          // If we've added an additional wrapper div, remove that from the DOM.\n          // If not, just remove the iframe element.\n          if (_this5.element.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== _this5.element.parentNode) {\n            _this5.element.parentNode.parentNode.removeChild(_this5.element.parentNode);\n          } else {\n            _this5.element.parentNode.removeChild(_this5.element);\n          }\n        }\n\n        // If the clip is private there is a case where the element stays the\n        // div element. Destroy should reset the div and remove the iframe child.\n        if (_this5.element && _this5.element.nodeName === 'DIV' && _this5.element.parentNode) {\n          _this5.element.removeAttribute('data-vimeo-initialized');\n          var iframe = _this5.element.querySelector('iframe');\n          if (iframe && iframe.parentNode) {\n            // If we've added an additional wrapper div, remove that from the DOM.\n            // If not, just remove the iframe element.\n            if (iframe.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== iframe.parentNode) {\n              iframe.parentNode.parentNode.removeChild(iframe.parentNode);\n            } else {\n              iframe.parentNode.removeChild(iframe);\n            }\n          }\n        }\n        _this5._window.removeEventListener('message', _this5._onMessage);\n        if (screenfull.isEnabled) {\n          screenfull.off('fullscreenchange', _this5.fullscreenchangeHandler);\n        }\n        resolve();\n      });\n    }\n\n    /**\n     * A promise to get the autopause behavior of the video.\n     *\n     * @promise GetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get the autopause behavior for this player.\n     *\n     * @return {GetAutopausePromise}\n     */\n  }, {\n    key: \"getAutopause\",\n    value: function getAutopause() {\n      return this.get('autopause');\n    }\n\n    /**\n     * A promise to set the autopause behavior of the video.\n     *\n     * @promise SetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Enable or disable the autopause behavior of this player.\n     *\n     * By default, when another video is played in the same browser, this\n     * player will automatically pause. Unless you have a specific reason\n     * for doing so, we recommend that you leave autopause set to the\n     * default (`true`).\n     *\n     * @param {boolean} autopause\n     * @return {SetAutopausePromise}\n     */\n  }, {\n    key: \"setAutopause\",\n    value: function setAutopause(autopause) {\n      return this.set('autopause', autopause);\n    }\n\n    /**\n     * A promise to get the buffered property of the video.\n     *\n     * @promise GetBufferedPromise\n     * @fulfill {Array} Buffered Timeranges converted to an Array.\n     */\n    /**\n     * Get the buffered property of the video.\n     *\n     * @return {GetBufferedPromise}\n     */\n  }, {\n    key: \"getBuffered\",\n    value: function getBuffered() {\n      return this.get('buffered');\n    }\n\n    /**\n     * @typedef {Object} CameraProperties\n     * @prop {number} props.yaw - Number between 0 and 360.\n     * @prop {number} props.pitch - Number between -90 and 90.\n     * @prop {number} props.roll - Number between -180 and 180.\n     * @prop {number} props.fov - The field of view in degrees.\n     */\n    /**\n     * A promise to get the camera properties of the player.\n     *\n     * @promise GetCameraPromise\n     * @fulfill {CameraProperties} The camera properties.\n     */\n    /**\n     * For 360° videos get the camera properties for this player.\n     *\n     * @return {GetCameraPromise}\n     */\n  }, {\n    key: \"getCameraProps\",\n    value: function getCameraProps() {\n      return this.get('cameraProps');\n    }\n\n    /**\n     * A promise to set the camera properties of the player.\n     *\n     * @promise SetCameraPromise\n     * @fulfill {Object} The camera was successfully set.\n     * @reject {RangeError} The range was out of bounds.\n     */\n    /**\n     * For 360° videos set the camera properties for this player.\n     *\n     * @param {CameraProperties} camera The camera properties\n     * @return {SetCameraPromise}\n     */\n  }, {\n    key: \"setCameraProps\",\n    value: function setCameraProps(camera) {\n      return this.set('cameraProps', camera);\n    }\n\n    /**\n     * A representation of a chapter.\n     *\n     * @typedef {Object} VimeoChapter\n     * @property {number} startTime The start time of the chapter.\n     * @property {object} title The title of the chapter.\n     * @property {number} index The place in the order of Chapters. Starts at 1.\n     */\n    /**\n     * A promise to get chapters for the video.\n     *\n     * @promise GetChaptersPromise\n     * @fulfill {VimeoChapter[]} The chapters for the video.\n     */\n    /**\n     * Get an array of all the chapters for the video.\n     *\n     * @return {GetChaptersPromise}\n     */\n  }, {\n    key: \"getChapters\",\n    value: function getChapters() {\n      return this.get('chapters');\n    }\n\n    /**\n     * A promise to get the currently active chapter.\n     *\n     * @promise GetCurrentChaptersPromise\n     * @fulfill {VimeoChapter|undefined} The current chapter for the video.\n     */\n    /**\n     * Get the currently active chapter for the video.\n     *\n     * @return {GetCurrentChaptersPromise}\n     */\n  }, {\n    key: \"getCurrentChapter\",\n    value: function getCurrentChapter() {\n      return this.get('currentChapter');\n    }\n\n    /**\n     * A promise to get the accent color of the player.\n     *\n     * @promise GetColorPromise\n     * @fulfill {string} The hex color of the player.\n     */\n    /**\n     * Get the accent color for this player. Note this is deprecated in place of `getColorTwo`.\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColor\",\n    value: function getColor() {\n      return this.get('color');\n    }\n\n    /**\n     * A promise to get all colors for the player in an array.\n     *\n     * @promise GetColorsPromise\n     * @fulfill {string[]} The hex colors of the player.\n     */\n    /**\n     * Get all the colors for this player in an array: [colorOne, colorTwo, colorThree, colorFour]\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColors\",\n    value: function getColors() {\n      return npo_src.all([this.get('colorOne'), this.get('colorTwo'), this.get('colorThree'), this.get('colorFour')]);\n    }\n\n    /**\n     * A promise to set the accent color of the player.\n     *\n     * @promise SetColorPromise\n     * @fulfill {string} The color was successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the accent color of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * Note this is deprecated in place of `setColorTwo`.\n     *\n     * @param {string} color The hex or rgb color string to set.\n     * @return {SetColorPromise}\n     */\n  }, {\n    key: \"setColor\",\n    value: function setColor(color) {\n      return this.set('color', color);\n    }\n\n    /**\n     * A promise to set all colors for the player.\n     *\n     * @promise SetColorsPromise\n     * @fulfill {string[]} The colors were successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the colors of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * The colors should be passed in as an array: [colorOne, colorTwo, colorThree, colorFour].\n     * If a color should not be set, the index in the array can be left as null.\n     *\n     * @param {string[]} colors Array of the hex or rgb color strings to set.\n     * @return {SetColorsPromise}\n     */\n  }, {\n    key: \"setColors\",\n    value: function setColors(colors) {\n      if (!Array.isArray(colors)) {\n        return new npo_src(function (resolve, reject) {\n          return reject(new TypeError('Argument must be an array.'));\n        });\n      }\n      var nullPromise = new npo_src(function (resolve) {\n        return resolve(null);\n      });\n      var colorPromises = [colors[0] ? this.set('colorOne', colors[0]) : nullPromise, colors[1] ? this.set('colorTwo', colors[1]) : nullPromise, colors[2] ? this.set('colorThree', colors[2]) : nullPromise, colors[3] ? this.set('colorFour', colors[3]) : nullPromise];\n      return npo_src.all(colorPromises);\n    }\n\n    /**\n     * A representation of a cue point.\n     *\n     * @typedef {Object} VimeoCuePoint\n     * @property {number} time The time of the cue point.\n     * @property {object} data The data passed when adding the cue point.\n     * @property {string} id The unique id for use with removeCuePoint.\n     */\n    /**\n     * A promise to get the cue points of a video.\n     *\n     * @promise GetCuePointsPromise\n     * @fulfill {VimeoCuePoint[]} The cue points added to the video.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get an array of the cue points added to the video.\n     *\n     * @return {GetCuePointsPromise}\n     */\n  }, {\n    key: \"getCuePoints\",\n    value: function getCuePoints() {\n      return this.get('cuePoints');\n    }\n\n    /**\n     * A promise to get the current time of the video.\n     *\n     * @promise GetCurrentTimePromise\n     * @fulfill {number} The current time in seconds.\n     */\n    /**\n     * Get the current playback position in seconds.\n     *\n     * @return {GetCurrentTimePromise}\n     */\n  }, {\n    key: \"getCurrentTime\",\n    value: function getCurrentTime() {\n      return this.get('currentTime');\n    }\n\n    /**\n     * A promise to set the current time of the video.\n     *\n     * @promise SetCurrentTimePromise\n     * @fulfill {number} The actual current time that was set.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     */\n    /**\n     * Set the current playback position in seconds. If the player was\n     * paused, it will remain paused. Likewise, if the player was playing,\n     * it will resume playing once the video has buffered.\n     *\n     * You can provide an accurate time and the player will attempt to seek\n     * to as close to that time as possible. The exact time will be the\n     * fulfilled value of the promise.\n     *\n     * @param {number} currentTime\n     * @return {SetCurrentTimePromise}\n     */\n  }, {\n    key: \"setCurrentTime\",\n    value: function setCurrentTime(currentTime) {\n      return this.set('currentTime', currentTime);\n    }\n\n    /**\n     * A promise to get the duration of the video.\n     *\n     * @promise GetDurationPromise\n     * @fulfill {number} The duration in seconds.\n     */\n    /**\n     * Get the duration of the video in seconds. It will be rounded to the\n     * nearest second before playback begins, and to the nearest thousandth\n     * of a second after playback begins.\n     *\n     * @return {GetDurationPromise}\n     */\n  }, {\n    key: \"getDuration\",\n    value: function getDuration() {\n      return this.get('duration');\n    }\n\n    /**\n     * A promise to get the ended state of the video.\n     *\n     * @promise GetEndedPromise\n     * @fulfill {boolean} Whether or not the video has ended.\n     */\n    /**\n     * Get the ended state of the video. The video has ended if\n     * `currentTime === duration`.\n     *\n     * @return {GetEndedPromise}\n     */\n  }, {\n    key: \"getEnded\",\n    value: function getEnded() {\n      return this.get('ended');\n    }\n\n    /**\n     * A promise to get the loop state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the player is set to loop.\n     */\n    /**\n     * Get the loop state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getLoop\",\n    value: function getLoop() {\n      return this.get('loop');\n    }\n\n    /**\n     * A promise to set the loop state of the player.\n     *\n     * @promise SetLoopPromise\n     * @fulfill {boolean} The loop state that was set.\n     */\n    /**\n     * Set the loop state of the player. When set to `true`, the player\n     * will start over immediately once playback ends.\n     *\n     * @param {boolean} loop\n     * @return {SetLoopPromise}\n     */\n  }, {\n    key: \"setLoop\",\n    value: function setLoop(loop) {\n      return this.set('loop', loop);\n    }\n\n    /**\n     * A promise to set the muted state of the player.\n     *\n     * @promise SetMutedPromise\n     * @fulfill {boolean} The muted state that was set.\n     */\n    /**\n     * Set the muted state of the player. When set to `true`, the player\n     * volume will be muted.\n     *\n     * @param {boolean} muted\n     * @return {SetMutedPromise}\n     */\n  }, {\n    key: \"setMuted\",\n    value: function setMuted(muted) {\n      return this.set('muted', muted);\n    }\n\n    /**\n     * A promise to get the muted state of the player.\n     *\n     * @promise GetMutedPromise\n     * @fulfill {boolean} Whether or not the player is muted.\n     */\n    /**\n     * Get the muted state of the player.\n     *\n     * @return {GetMutedPromise}\n     */\n  }, {\n    key: \"getMuted\",\n    value: function getMuted() {\n      return this.get('muted');\n    }\n\n    /**\n     * A promise to get the paused state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the video is paused.\n     */\n    /**\n     * Get the paused state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getPaused\",\n    value: function getPaused() {\n      return this.get('paused');\n    }\n\n    /**\n     * A promise to get the playback rate of the player.\n     *\n     * @promise GetPlaybackRatePromise\n     * @fulfill {number} The playback rate of the player on a scale from 0 to 2.\n     */\n    /**\n     * Get the playback rate of the player on a scale from `0` to `2`.\n     *\n     * @return {GetPlaybackRatePromise}\n     */\n  }, {\n    key: \"getPlaybackRate\",\n    value: function getPlaybackRate() {\n      return this.get('playbackRate');\n    }\n\n    /**\n     * A promise to set the playbackrate of the player.\n     *\n     * @promise SetPlaybackRatePromise\n     * @fulfill {number} The playback rate was set.\n     * @reject {RangeError} The playback rate was less than 0 or greater than 2.\n     */\n    /**\n     * Set the playback rate of the player on a scale from `0` to `2`. When set\n     * via the API, the playback rate will not be synchronized to other\n     * players or stored as the viewer's preference.\n     *\n     * @param {number} playbackRate\n     * @return {SetPlaybackRatePromise}\n     */\n  }, {\n    key: \"setPlaybackRate\",\n    value: function setPlaybackRate(playbackRate) {\n      return this.set('playbackRate', playbackRate);\n    }\n\n    /**\n     * A promise to get the played property of the video.\n     *\n     * @promise GetPlayedPromise\n     * @fulfill {Array} Played Timeranges converted to an Array.\n     */\n    /**\n     * Get the played property of the video.\n     *\n     * @return {GetPlayedPromise}\n     */\n  }, {\n    key: \"getPlayed\",\n    value: function getPlayed() {\n      return this.get('played');\n    }\n\n    /**\n     * A promise to get the qualities available of the current video.\n     *\n     * @promise GetQualitiesPromise\n     * @fulfill {Array} The qualities of the video.\n     */\n    /**\n     * Get the qualities of the current video.\n     *\n     * @return {GetQualitiesPromise}\n     */\n  }, {\n    key: \"getQualities\",\n    value: function getQualities() {\n      return this.get('qualities');\n    }\n\n    /**\n     * A promise to get the current set quality of the video.\n     *\n     * @promise GetQualityPromise\n     * @fulfill {string} The current set quality.\n     */\n    /**\n     * Get the current set quality of the video.\n     *\n     * @return {GetQualityPromise}\n     */\n  }, {\n    key: \"getQuality\",\n    value: function getQuality() {\n      return this.get('quality');\n    }\n\n    /**\n     * A promise to set the video quality.\n     *\n     * @promise SetQualityPromise\n     * @fulfill {number} The quality was set.\n     * @reject {RangeError} The quality is not available.\n     */\n    /**\n     * Set a video quality.\n     *\n     * @param {string} quality\n     * @return {SetQualityPromise}\n     */\n  }, {\n    key: \"setQuality\",\n    value: function setQuality(quality) {\n      return this.set('quality', quality);\n    }\n\n    /**\n     * A promise to get the remote playback availability.\n     *\n     * @promise RemotePlaybackAvailabilityPromise\n     * @fulfill {boolean} Whether remote playback is available.\n     */\n    /**\n     * Get the availability of remote playback.\n     *\n     * @return {RemotePlaybackAvailabilityPromise}\n     */\n  }, {\n    key: \"getRemotePlaybackAvailability\",\n    value: function getRemotePlaybackAvailability() {\n      return this.get('remotePlaybackAvailability');\n    }\n\n    /**\n     * A promise to get the current remote playback state.\n     *\n     * @promise RemotePlaybackStatePromise\n     * @fulfill {string} The state of the remote playback: connecting, connected, or disconnected.\n     */\n    /**\n     * Get the current remote playback state.\n     *\n     * @return {RemotePlaybackStatePromise}\n     */\n  }, {\n    key: \"getRemotePlaybackState\",\n    value: function getRemotePlaybackState() {\n      return this.get('remotePlaybackState');\n    }\n\n    /**\n     * A promise to get the seekable property of the video.\n     *\n     * @promise GetSeekablePromise\n     * @fulfill {Array} Seekable Timeranges converted to an Array.\n     */\n    /**\n     * Get the seekable property of the video.\n     *\n     * @return {GetSeekablePromise}\n     */\n  }, {\n    key: \"getSeekable\",\n    value: function getSeekable() {\n      return this.get('seekable');\n    }\n\n    /**\n     * A promise to get the seeking property of the player.\n     *\n     * @promise GetSeekingPromise\n     * @fulfill {boolean} Whether or not the player is currently seeking.\n     */\n    /**\n     * Get if the player is currently seeking.\n     *\n     * @return {GetSeekingPromise}\n     */\n  }, {\n    key: \"getSeeking\",\n    value: function getSeeking() {\n      return this.get('seeking');\n    }\n\n    /**\n     * A promise to get the text tracks of a video.\n     *\n     * @promise GetTextTracksPromise\n     * @fulfill {VimeoTextTrack[]} The text tracks associated with the video.\n     */\n    /**\n     * Get an array of the text tracks that exist for the video.\n     *\n     * @return {GetTextTracksPromise}\n     */\n  }, {\n    key: \"getTextTracks\",\n    value: function getTextTracks() {\n      return this.get('textTracks');\n    }\n\n    /**\n     * A promise to get the embed code for the video.\n     *\n     * @promise GetVideoEmbedCodePromise\n     * @fulfill {string} The `<iframe>` embed code for the video.\n     */\n    /**\n     * Get the `<iframe>` embed code for the video.\n     *\n     * @return {GetVideoEmbedCodePromise}\n     */\n  }, {\n    key: \"getVideoEmbedCode\",\n    value: function getVideoEmbedCode() {\n      return this.get('videoEmbedCode');\n    }\n\n    /**\n     * A promise to get the id of the video.\n     *\n     * @promise GetVideoIdPromise\n     * @fulfill {number} The id of the video.\n     */\n    /**\n     * Get the id of the video.\n     *\n     * @return {GetVideoIdPromise}\n     */\n  }, {\n    key: \"getVideoId\",\n    value: function getVideoId() {\n      return this.get('videoId');\n    }\n\n    /**\n     * A promise to get the title of the video.\n     *\n     * @promise GetVideoTitlePromise\n     * @fulfill {number} The title of the video.\n     */\n    /**\n     * Get the title of the video.\n     *\n     * @return {GetVideoTitlePromise}\n     */\n  }, {\n    key: \"getVideoTitle\",\n    value: function getVideoTitle() {\n      return this.get('videoTitle');\n    }\n\n    /**\n     * A promise to get the native width of the video.\n     *\n     * @promise GetVideoWidthPromise\n     * @fulfill {number} The native width of the video.\n     */\n    /**\n     * Get the native width of the currently‐playing video. The width of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoWidthPromise}\n     */\n  }, {\n    key: \"getVideoWidth\",\n    value: function getVideoWidth() {\n      return this.get('videoWidth');\n    }\n\n    /**\n     * A promise to get the native height of the video.\n     *\n     * @promise GetVideoHeightPromise\n     * @fulfill {number} The native height of the video.\n     */\n    /**\n     * Get the native height of the currently‐playing video. The height of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoHeightPromise}\n     */\n  }, {\n    key: \"getVideoHeight\",\n    value: function getVideoHeight() {\n      return this.get('videoHeight');\n    }\n\n    /**\n     * A promise to get the vimeo.com url for the video.\n     *\n     * @promise GetVideoUrlPromise\n     * @fulfill {number} The vimeo.com url for the video.\n     * @reject {PrivacyError} The url isn’t available because of the video’s privacy setting.\n     */\n    /**\n     * Get the vimeo.com url for the video.\n     *\n     * @return {GetVideoUrlPromise}\n     */\n  }, {\n    key: \"getVideoUrl\",\n    value: function getVideoUrl() {\n      return this.get('videoUrl');\n    }\n\n    /**\n     * A promise to get the volume level of the player.\n     *\n     * @promise GetVolumePromise\n     * @fulfill {number} The volume level of the player on a scale from 0 to 1.\n     */\n    /**\n     * Get the current volume level of the player on a scale from `0` to `1`.\n     *\n     * Most mobile devices do not support an independent volume from the\n     * system volume. In those cases, this method will always return `1`.\n     *\n     * @return {GetVolumePromise}\n     */\n  }, {\n    key: \"getVolume\",\n    value: function getVolume() {\n      return this.get('volume');\n    }\n\n    /**\n     * A promise to set the volume level of the player.\n     *\n     * @promise SetVolumePromise\n     * @fulfill {number} The volume was set.\n     * @reject {RangeError} The volume was less than 0 or greater than 1.\n     */\n    /**\n     * Set the volume of the player on a scale from `0` to `1`. When set\n     * via the API, the volume level will not be synchronized to other\n     * players or stored as the viewer’s preference.\n     *\n     * Most mobile devices do not support setting the volume. An error will\n     * *not* be triggered in that situation.\n     *\n     * @param {number} volume\n     * @return {SetVolumePromise}\n     */\n  }, {\n    key: \"setVolume\",\n    value: function setVolume(volume) {\n      return this.set('volume', volume);\n    }\n\n    /** @typedef {import('./lib/timing-object.types').TimingObject} TimingObject */\n    /** @typedef {import('./lib/timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n    /** @typedef {import('./lib/timing-src-connector').TimingSrcConnector} TimingSrcConnector */\n\n    /**\n     * Connects a TimingObject to the video player (https://webtiming.github.io/timingobject/)\n     *\n     * @param {TimingObject} timingObject\n     * @param {TimingSrcConnectorOptions} options\n     *\n     * @return {Promise<TimingSrcConnector>}\n     */\n  }, {\n    key: \"setTimingSrc\",\n    value: function () {\n      var _setTimingSrc = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(timingObject, options) {\n        var _this6 = this;\n        var connector;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (timingObject) {\n                _context.next = 2;\n                break;\n              }\n              throw new TypeError('A Timing Object must be provided.');\n            case 2:\n              _context.next = 4;\n              return this.ready();\n            case 4:\n              connector = new TimingSrcConnector(this, timingObject, options);\n              postMessage(this, 'notifyTimingObjectConnect');\n              connector.addEventListener('disconnect', function () {\n                return postMessage(_this6, 'notifyTimingObjectDisconnect');\n              });\n              return _context.abrupt(\"return\", connector);\n            case 8:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function setTimingSrc(_x, _x2) {\n        return _setTimingSrc.apply(this, arguments);\n      }\n      return setTimingSrc;\n    }()\n  }]);\n  return Player;\n}(); // Setup embed only if this is not a node environment\nif (!isNode) {\n  screenfull = initializeScreenfull();\n  initializeEmbeds();\n  resizeEmbeds();\n  initAppendVideoMetadata();\n  checkUrlTimeParam();\n}\n\nexport default Player;\n", "import VimeoPlayerAPI from \"@vimeo/player/dist/player.es.js\";\nconst EMBED_BASE = \"https://player.vimeo.com/video\";\nconst MATCH_SRC = /vimeo\\.com\\/(?:video\\/)?(\\d+)(?:\\/([\\w-]+))?/;\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n      }\n      :host(:not([controls])) {\n        pointer-events: none;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const srcId = matches && matches[1];\n  const hParam = matches && matches[2];\n  const params = {\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    loop: attrs.loop,\n    muted: attrs.muted,\n    playsinline: attrs.playsinline,\n    preload: attrs.preload ?? \"metadata\",\n    transparent: false,\n    autopause: attrs.autopause,\n    h: hParam,\n    // This param is required when the video is Unlisted.\n    ...props.config\n  };\n  return `${EMBED_BASE}/${srcId}?${serialize(params)}`;\n}\nclass VimeoVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"autoplay\",\n    \"controls\",\n    \"crossorigin\",\n    \"loop\",\n    \"muted\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #isInit;\n  #currentTime = 0;\n  #duration = NaN;\n  #muted = false;\n  #paused = !this.autoplay;\n  #playbackRate = 1;\n  #progress = 0;\n  #readyState = 0;\n  #seeking = false;\n  #volume = 1;\n  #videoWidth = NaN;\n  #videoHeight = NaN;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  requestFullscreen() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.requestFullscreen) == null ? void 0 : _b.call(_a);\n  }\n  exitFullscreen() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.exitFullscreen) == null ? void 0 : _b.call(_a);\n  }\n  requestPictureInPicture() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.requestPictureInPicture) == null ? void 0 : _b.call(_a);\n  }\n  exitPictureInPicture() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.exitPictureInPicture) == null ? void 0 : _b.call(_a);\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    var _a;\n    if (this.#loadRequested) return;\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#currentTime = 0;\n    this.#duration = NaN;\n    this.#muted = false;\n    this.#paused = !this.autoplay;\n    this.#playbackRate = 1;\n    this.#progress = 0;\n    this.#readyState = 0;\n    this.#seeking = false;\n    this.#volume = 1;\n    this.#readyState = 0;\n    this.#videoWidth = NaN;\n    this.#videoHeight = NaN;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    const options = {\n      autoplay: this.autoplay,\n      controls: this.controls,\n      loop: this.loop,\n      muted: this.defaultMuted,\n      playsinline: this.playsInline,\n      preload: this.preload ?? \"metadata\",\n      transparent: false,\n      autopause: this.hasAttribute(\"autopause\"),\n      ...this.#config\n    };\n    const onLoaded = async () => {\n      this.#readyState = 1;\n      this.dispatchEvent(new Event(\"loadedmetadata\"));\n      if (this.api) {\n        this.#muted = await this.api.getMuted();\n        this.#volume = await this.api.getVolume();\n        this.dispatchEvent(new Event(\"volumechange\"));\n        this.#duration = await this.api.getDuration();\n        this.dispatchEvent(new Event(\"durationchange\"));\n      }\n      this.dispatchEvent(new Event(\"loadcomplete\"));\n      this.loadComplete.resolve();\n    };\n    if (this.#isInit) {\n      this.api = oldApi;\n      await this.api.loadVideo({\n        ...options,\n        url: this.src\n      });\n      await onLoaded();\n      await this.loadComplete;\n      return;\n    }\n    this.#isInit = true;\n    let iframe = (_a = this.shadowRoot) == null ? void 0 : _a.querySelector(\"iframe\");\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n      this.shadowRoot.innerHTML = getTemplateHTML(namedNodeMapToObject(this.attributes), this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.api = new VimeoPlayerAPI(iframe);\n    const onceLoaded = () => {\n      this.api.off(\"loaded\", onceLoaded);\n      onLoaded();\n    };\n    this.api.on(\"loaded\", onceLoaded);\n    this.api.on(\"bufferstart\", () => {\n      if (this.#paused) {\n        this.#paused = false;\n        this.dispatchEvent(new Event(\"play\"));\n      }\n      this.dispatchEvent(new Event(\"waiting\"));\n    });\n    this.api.on(\"play\", () => {\n      if (!this.#paused) return;\n      this.#paused = false;\n      this.dispatchEvent(new Event(\"play\"));\n    });\n    this.api.on(\"playing\", () => {\n      this.#readyState = 3;\n      this.#paused = false;\n      this.dispatchEvent(new Event(\"playing\"));\n    });\n    this.api.on(\"seeking\", () => {\n      this.#seeking = true;\n      this.dispatchEvent(new Event(\"seeking\"));\n    });\n    this.api.on(\"seeked\", () => {\n      this.#seeking = false;\n      this.dispatchEvent(new Event(\"seeked\"));\n    });\n    this.api.on(\"pause\", () => {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"pause\"));\n    });\n    this.api.on(\"ended\", () => {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"ended\"));\n    });\n    this.api.on(\"ratechange\", ({ playbackRate }) => {\n      this.#playbackRate = playbackRate;\n      this.dispatchEvent(new Event(\"ratechange\"));\n    });\n    this.api.on(\"volumechange\", async ({ volume }) => {\n      this.#volume = volume;\n      if (this.api) {\n        this.#muted = await this.api.getMuted();\n      }\n      this.dispatchEvent(new Event(\"volumechange\"));\n    });\n    this.api.on(\"durationchange\", ({ duration }) => {\n      this.#duration = duration;\n      this.dispatchEvent(new Event(\"durationchange\"));\n    });\n    this.api.on(\"timeupdate\", ({ seconds }) => {\n      this.#currentTime = seconds;\n      this.dispatchEvent(new Event(\"timeupdate\"));\n    });\n    this.api.on(\"progress\", ({ seconds }) => {\n      this.#progress = seconds;\n      this.dispatchEvent(new Event(\"progress\"));\n    });\n    this.api.on(\"resize\", ({ videoWidth, videoHeight }) => {\n      this.#videoWidth = videoWidth;\n      this.#videoHeight = videoHeight;\n      this.dispatchEvent(new Event(\"resize\"));\n    });\n    await this.loadComplete;\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"autoplay\":\n      case \"controls\":\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n    await this.loadComplete;\n    switch (attrName) {\n      case \"loop\": {\n        this.api.setLoop(this.loop);\n        break;\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#paused = false;\n    this.dispatchEvent(new Event(\"play\"));\n    await this.loadComplete;\n    try {\n      await ((_a = this.api) == null ? void 0 : _a.play());\n    } catch (error) {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"pause\"));\n      throw error;\n    }\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pause();\n  }\n  get ended() {\n    return this.#currentTime >= this.#duration;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get videoWidth() {\n    return this.#videoWidth;\n  }\n  get videoHeight() {\n    return this.#videoHeight;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    if (this.src == val) return;\n    this.setAttribute(\"src\", val);\n  }\n  get paused() {\n    return this.#paused;\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get buffered() {\n    if (this.#progress > 0) {\n      return createTimeRanges(0, this.#progress);\n    }\n    return createTimeRanges();\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#currentTime = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setCurrentTime(val).catch(() => {\n      });\n    });\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    if (this.defaultMuted == val) return;\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    if (this.muted == val) return;\n    this.#muted = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setMuted(val).catch(() => {\n      });\n    });\n  }\n  get playbackRate() {\n    return this.#playbackRate;\n  }\n  set playbackRate(val) {\n    if (this.playbackRate == val) return;\n    this.#playbackRate = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setPlaybackRate(val).catch(() => {\n      });\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    if (this.playsInline == val) return;\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  get poster() {\n    return this.getAttribute(\"poster\");\n  }\n  set poster(val) {\n    if (this.poster == val) return;\n    this.setAttribute(\"poster\", `${val}`);\n  }\n  get volume() {\n    return this.#volume;\n  }\n  set volume(val) {\n    if (this.volume == val) return;\n    this.#volume = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setVolume(val).catch(() => {\n      });\n    });\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"vimeo-video\")) {\n  globalThis.customElements.define(\"vimeo-video\", VimeoVideoElement);\n}\nvar vimeo_video_element_default = VimeoVideoElement;\nexport {\n  vimeo_video_element_default as default\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACFlB,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,sBAAsB;AAC7B,wBAAsB,WAAY;AAChC,WAAO;AAAA,EACT;AACA,MAAI,UAAU,CAAC,GACb,KAAK,OAAO,WACZ,SAAS,GAAG,gBACZ,iBAAiB,OAAO,kBAAkB,SAAU,KAAK,KAAK,MAAM;AAClE,QAAI,GAAG,IAAI,KAAK;AAAA,EAClB,GACA,UAAU,cAAc,OAAO,SAAS,SAAS,CAAC,GAClD,iBAAiB,QAAQ,YAAY,cACrC,sBAAsB,QAAQ,iBAAiB,mBAC/C,oBAAoB,QAAQ,eAAe;AAC7C,WAAS,OAAO,KAAK,KAAK,OAAO;AAC/B,WAAO,OAAO,eAAe,KAAK,KAAK;AAAA,MACrC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,IAAI,GAAG;AAAA,EACb;AACA,MAAI;AACF,WAAO,CAAC,GAAG,EAAE;AAAA,EACf,SAAS,KAAK;AACZ,aAAS,SAAU,KAAK,KAAK,OAAO;AAClC,aAAO,IAAI,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,WAAS,KAAK,SAAS,SAASA,OAAM,aAAa;AACjD,QAAI,iBAAiB,WAAW,QAAQ,qBAAqB,YAAY,UAAU,WACjF,YAAY,OAAO,OAAO,eAAe,SAAS,GAClD,UAAU,IAAI,QAAQ,eAAe,CAAC,CAAC;AACzC,WAAO,eAAe,WAAW,WAAW;AAAA,MAC1C,OAAO,iBAAiB,SAASA,OAAM,OAAO;AAAA,IAChD,CAAC,GAAG;AAAA,EACN;AACA,WAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,QAAI;AACF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,GAAG,KAAK,KAAK,GAAG;AAAA,MACvB;AAAA,IACF,SAAS,KAAK;AACZ,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,UAAQ,OAAO;AACf,MAAI,mBAAmB,CAAC;AACxB,WAAS,YAAY;AAAA,EAAC;AACtB,WAAS,oBAAoB;AAAA,EAAC;AAC9B,WAAS,6BAA6B;AAAA,EAAC;AACvC,MAAI,oBAAoB,CAAC;AACzB,SAAO,mBAAmB,gBAAgB,WAAY;AACpD,WAAO;AAAA,EACT,CAAC;AACD,MAAI,WAAW,OAAO,gBACpB,0BAA0B,YAAY,SAAS,SAAS,OAAO,CAAC,CAAC,CAAC,CAAC;AACrE,6BAA2B,4BAA4B,MAAM,OAAO,KAAK,yBAAyB,cAAc,MAAM,oBAAoB;AAC1I,MAAI,KAAK,2BAA2B,YAAY,UAAU,YAAY,OAAO,OAAO,iBAAiB;AACrG,WAAS,sBAAsB,WAAW;AACxC,KAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAU,QAAQ;AACpD,aAAO,WAAW,QAAQ,SAAU,KAAK;AACvC,eAAO,KAAK,QAAQ,QAAQ,GAAG;AAAA,MACjC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,WAAS,cAAc,WAAW,aAAa;AAC7C,aAAS,OAAO,QAAQ,KAAK,SAAS,QAAQ;AAC5C,UAAI,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,GAAG;AACvD,UAAI,YAAY,OAAO,MAAM;AAC3B,YAAI,SAAS,OAAO,KAClB,QAAQ,OAAO;AACjB,eAAO,SAAS,YAAY,OAAO,SAAS,OAAO,KAAK,OAAO,SAAS,IAAI,YAAY,QAAQ,MAAM,OAAO,EAAE,KAAK,SAAUC,QAAO;AACnI,iBAAO,QAAQA,QAAO,SAAS,MAAM;AAAA,QACvC,GAAG,SAAU,KAAK;AAChB,iBAAO,SAAS,KAAK,SAAS,MAAM;AAAA,QACtC,CAAC,IAAI,YAAY,QAAQ,KAAK,EAAE,KAAK,SAAU,WAAW;AACxD,iBAAO,QAAQ,WAAW,QAAQ,MAAM;AAAA,QAC1C,GAAG,SAAU,OAAO;AAClB,iBAAO,OAAO,SAAS,OAAO,SAAS,MAAM;AAAA,QAC/C,CAAC;AAAA,MACH;AACA,aAAO,OAAO,GAAG;AAAA,IACnB;AACA,QAAI;AACJ,mBAAe,MAAM,WAAW;AAAA,MAC9B,OAAO,SAAU,QAAQ,KAAK;AAC5B,iBAAS,6BAA6B;AACpC,iBAAO,IAAI,YAAY,SAAU,SAAS,QAAQ;AAChD,mBAAO,QAAQ,KAAK,SAAS,MAAM;AAAA,UACrC,CAAC;AAAA,QACH;AACA,eAAO,kBAAkB,kBAAkB,gBAAgB,KAAK,4BAA4B,0BAA0B,IAAI,2BAA2B;AAAA,MACvJ;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,SAASD,OAAM,SAAS;AAChD,QAAI,QAAQ;AACZ,WAAO,SAAU,QAAQ,KAAK;AAC5B,UAAI,gBAAgB,MAAO,OAAM,IAAI,MAAM,8BAA8B;AACzE,UAAI,gBAAgB,OAAO;AACzB,YAAI,YAAY,OAAQ,OAAM;AAC9B,eAAO,WAAW;AAAA,MACpB;AACA,WAAK,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAO;AACjD,YAAI,WAAW,QAAQ;AACvB,YAAI,UAAU;AACZ,cAAI,iBAAiB,oBAAoB,UAAU,OAAO;AAC1D,cAAI,gBAAgB;AAClB,gBAAI,mBAAmB,iBAAkB;AACzC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,WAAW,QAAQ,OAAQ,SAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAAA,iBAAa,YAAY,QAAQ,QAAQ;AAC7G,cAAI,qBAAqB,MAAO,OAAM,QAAQ,aAAa,QAAQ;AACnE,kBAAQ,kBAAkB,QAAQ,GAAG;AAAA,QACvC,MAAO,cAAa,QAAQ,UAAU,QAAQ,OAAO,UAAU,QAAQ,GAAG;AAC1E,gBAAQ;AACR,YAAI,SAAS,SAAS,SAASA,OAAM,OAAO;AAC5C,YAAI,aAAa,OAAO,MAAM;AAC5B,cAAI,QAAQ,QAAQ,OAAO,cAAc,kBAAkB,OAAO,QAAQ,iBAAkB;AAC5F,iBAAO;AAAA,YACL,OAAO,OAAO;AAAA,YACd,MAAM,QAAQ;AAAA,UAChB;AAAA,QACF;AACA,oBAAY,OAAO,SAAS,QAAQ,aAAa,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO;AAAA,MAClG;AAAA,IACF;AAAA,EACF;AACA,WAAS,oBAAoB,UAAU,SAAS;AAC9C,QAAI,aAAa,QAAQ,QACvB,SAAS,SAAS,SAAS,UAAU;AACvC,QAAI,WAAc,OAAQ,QAAO,QAAQ,WAAW,MAAM,YAAY,cAAc,SAAS,SAAS,WAAW,QAAQ,SAAS,UAAU,QAAQ,MAAM,QAAW,oBAAoB,UAAU,OAAO,GAAG,YAAY,QAAQ,WAAW,aAAa,eAAe,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,sCAAsC,aAAa,UAAU,IAAI;AAC/X,QAAI,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,GAAG;AAC5D,QAAI,YAAY,OAAO,KAAM,QAAO,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO,KAAK,QAAQ,WAAW,MAAM;AACjH,QAAI,OAAO,OAAO;AAClB,WAAO,OAAO,KAAK,QAAQ,QAAQ,SAAS,UAAU,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,aAAa,QAAQ,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAY,QAAQ,WAAW,MAAM,oBAAoB,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,kCAAkC,GAAG,QAAQ,WAAW,MAAM;AAAA,EACrW;AACA,WAAS,aAAa,MAAM;AAC1B,QAAI,QAAQ;AAAA,MACV,QAAQ,KAAK,CAAC;AAAA,IAChB;AACA,SAAK,SAAS,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,SAAS,MAAM,aAAa,KAAK,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,EAC1I;AACA,WAAS,cAAc,OAAO;AAC5B,QAAI,SAAS,MAAM,cAAc,CAAC;AAClC,WAAO,OAAO,UAAU,OAAO,OAAO,KAAK,MAAM,aAAa;AAAA,EAChE;AACA,WAAS,QAAQ,aAAa;AAC5B,SAAK,aAAa,CAAC;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC,GAAG,YAAY,QAAQ,cAAc,IAAI,GAAG,KAAK,MAAM,IAAE;AAAA,EAC5D;AACA,WAAS,OAAO,UAAU;AACxB,QAAI,UAAU;AACZ,UAAI,iBAAiB,SAAS,cAAc;AAC5C,UAAI,eAAgB,QAAO,eAAe,KAAK,QAAQ;AACvD,UAAI,cAAc,OAAO,SAAS,KAAM,QAAO;AAC/C,UAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAC3B,YAAI,IAAI,IACN,OAAO,SAASE,QAAO;AACrB,iBAAO,EAAE,IAAI,SAAS,SAAS,KAAI,OAAO,KAAK,UAAU,CAAC,EAAG,QAAOA,MAAK,QAAQ,SAAS,CAAC,GAAGA,MAAK,OAAO,OAAIA;AAC9G,iBAAOA,MAAK,QAAQ,QAAWA,MAAK,OAAO,MAAIA;AAAA,QACjD;AACF,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AACA,WAAS,aAAa;AACpB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,kBAAkB,YAAY,4BAA4B,eAAe,IAAI,eAAe;AAAA,IACjG,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,eAAe,4BAA4B,eAAe;AAAA,IAC5D,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,kBAAkB,cAAc,OAAO,4BAA4B,mBAAmB,mBAAmB,GAAG,QAAQ,sBAAsB,SAAU,QAAQ;AAC9J,QAAI,OAAO,cAAc,OAAO,UAAU,OAAO;AACjD,WAAO,CAAC,CAAC,SAAS,SAAS,qBAAqB,yBAAyB,KAAK,eAAe,KAAK;AAAA,EACpG,GAAG,QAAQ,OAAO,SAAU,QAAQ;AAClC,WAAO,OAAO,iBAAiB,OAAO,eAAe,QAAQ,0BAA0B,KAAK,OAAO,YAAY,4BAA4B,OAAO,QAAQ,mBAAmB,mBAAmB,IAAI,OAAO,YAAY,OAAO,OAAO,EAAE,GAAG;AAAA,EAC5O,GAAG,QAAQ,QAAQ,SAAU,KAAK;AAChC,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF,GAAG,sBAAsB,cAAc,SAAS,GAAG,OAAO,cAAc,WAAW,qBAAqB,WAAY;AAClH,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,gBAAgB,eAAe,QAAQ,QAAQ,SAAU,SAAS,SAASF,OAAM,aAAa,aAAa;AACrH,eAAW,gBAAgB,cAAc;AACzC,QAAI,OAAO,IAAI,cAAc,KAAK,SAAS,SAASA,OAAM,WAAW,GAAG,WAAW;AACnF,WAAO,QAAQ,oBAAoB,OAAO,IAAI,OAAO,KAAK,KAAK,EAAE,KAAK,SAAU,QAAQ;AACtF,aAAO,OAAO,OAAO,OAAO,QAAQ,KAAK,KAAK;AAAA,IAChD,CAAC;AAAA,EACH,GAAG,sBAAsB,EAAE,GAAG,OAAO,IAAI,mBAAmB,WAAW,GAAG,OAAO,IAAI,gBAAgB,WAAY;AAC/G,WAAO;AAAA,EACT,CAAC,GAAG,OAAO,IAAI,YAAY,WAAY;AACrC,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,OAAO,SAAU,KAAK;AAChC,QAAI,SAAS,OAAO,GAAG,GACrB,OAAO,CAAC;AACV,aAAS,OAAO,OAAQ,MAAK,KAAK,GAAG;AACrC,WAAO,KAAK,QAAQ,GAAG,SAAS,OAAO;AACrC,aAAO,KAAK,UAAS;AACnB,YAAIG,OAAM,KAAK,IAAI;AACnB,YAAIA,QAAO,OAAQ,QAAO,KAAK,QAAQA,MAAK,KAAK,OAAO,OAAI;AAAA,MAC9D;AACA,aAAO,KAAK,OAAO,MAAI;AAAA,IACzB;AAAA,EACF,GAAG,QAAQ,SAAS,QAAQ,QAAQ,YAAY;AAAA,IAC9C,aAAa;AAAA,IACb,OAAO,SAAU,eAAe;AAC9B,UAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,QAAW,KAAK,OAAO,OAAI,KAAK,WAAW,MAAM,KAAK,SAAS,QAAQ,KAAK,MAAM,QAAW,KAAK,WAAW,QAAQ,aAAa,GAAG,CAAC,cAAe,UAAS,QAAQ,KAAM,SAAQ,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI;AAAA,IACtU;AAAA,IACA,MAAM,WAAY;AAChB,WAAK,OAAO;AACZ,UAAI,aAAa,KAAK,WAAW,CAAC,EAAE;AACpC,UAAI,YAAY,WAAW,KAAM,OAAM,WAAW;AAClD,aAAO,KAAK;AAAA,IACd;AAAA,IACA,mBAAmB,SAAU,WAAW;AACtC,UAAI,KAAK,KAAM,OAAM;AACrB,UAAI,UAAU;AACd,eAAS,OAAO,KAAK,QAAQ;AAC3B,eAAO,OAAO,OAAO,SAAS,OAAO,MAAM,WAAW,QAAQ,OAAO,KAAK,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAY,CAAC,CAAC;AAAA,MAC5I;AACA,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC,GAC3B,SAAS,MAAM;AACjB,YAAI,WAAW,MAAM,OAAQ,QAAO,OAAO,KAAK;AAChD,YAAI,MAAM,UAAU,KAAK,MAAM;AAC7B,cAAI,WAAW,OAAO,KAAK,OAAO,UAAU,GAC1C,aAAa,OAAO,KAAK,OAAO,YAAY;AAC9C,cAAI,YAAY,YAAY;AAC1B,gBAAI,KAAK,OAAO,MAAM,SAAU,QAAO,OAAO,MAAM,UAAU,IAAE;AAChE,gBAAI,KAAK,OAAO,MAAM,WAAY,QAAO,OAAO,MAAM,UAAU;AAAA,UAClE,WAAW,UAAU;AACnB,gBAAI,KAAK,OAAO,MAAM,SAAU,QAAO,OAAO,MAAM,UAAU,IAAE;AAAA,UAClE,OAAO;AACL,gBAAI,CAAC,WAAY,OAAM,IAAI,MAAM,wCAAwC;AACzE,gBAAI,KAAK,OAAO,MAAM,WAAY,QAAO,OAAO,MAAM,UAAU;AAAA,UAClE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ,SAAU,MAAM,KAAK;AAC3B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,UAAU,KAAK,QAAQ,OAAO,KAAK,OAAO,YAAY,KAAK,KAAK,OAAO,MAAM,YAAY;AACjG,cAAI,eAAe;AACnB;AAAA,QACF;AAAA,MACF;AACA,uBAAiB,YAAY,QAAQ,eAAe,SAAS,aAAa,UAAU,OAAO,OAAO,aAAa,eAAe,eAAe;AAC7I,UAAI,SAAS,eAAe,aAAa,aAAa,CAAC;AACvD,aAAO,OAAO,OAAO,MAAM,OAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK,OAAO,aAAa,YAAY,oBAAoB,KAAK,SAAS,MAAM;AAAA,IAClK;AAAA,IACA,UAAU,SAAU,QAAQ,UAAU;AACpC,UAAI,YAAY,OAAO,KAAM,OAAM,OAAO;AAC1C,aAAO,YAAY,OAAO,QAAQ,eAAe,OAAO,OAAO,KAAK,OAAO,OAAO,MAAM,aAAa,OAAO,QAAQ,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,SAAS,UAAU,KAAK,OAAO,SAAS,aAAa,OAAO,QAAQ,aAAa,KAAK,OAAO,WAAW;AAAA,IACtQ;AAAA,IACA,QAAQ,SAAU,YAAY;AAC5B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,eAAe,WAAY,QAAO,KAAK,SAAS,MAAM,YAAY,MAAM,QAAQ,GAAG,cAAc,KAAK,GAAG;AAAA,MACrH;AAAA,IACF;AAAA,IACA,OAAO,SAAU,QAAQ;AACvB,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,WAAW,QAAQ;AAC3B,cAAI,SAAS,MAAM;AACnB,cAAI,YAAY,OAAO,MAAM;AAC3B,gBAAI,SAAS,OAAO;AACpB,0BAAc,KAAK;AAAA,UACrB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAAA,IACA,eAAe,SAAU,UAAU,YAAY,SAAS;AACtD,aAAO,KAAK,WAAW;AAAA,QACrB,UAAU,OAAO,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,MACF,GAAG,WAAW,KAAK,WAAW,KAAK,MAAM,SAAY;AAAA,IACvD;AAAA,EACF,GAAG;AACL;AACA,SAAS,mBAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,KAAK,KAAK;AACzE,MAAI;AACF,QAAI,OAAO,IAAI,GAAG,EAAE,GAAG;AACvB,QAAI,QAAQ,KAAK;AAAA,EACnB,SAAS,OAAO;AACd,WAAO,KAAK;AACZ;AAAA,EACF;AACA,MAAI,KAAK,MAAM;AACb,YAAQ,KAAK;AAAA,EACf,OAAO;AACL,YAAQ,QAAQ,KAAK,EAAE,KAAK,OAAO,MAAM;AAAA,EAC3C;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,WAAY;AACjB,QAAIH,QAAO,MACT,OAAO;AACT,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,UAAI,MAAM,GAAG,MAAMA,OAAM,IAAI;AAC7B,eAAS,MAAM,OAAO;AACpB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAAA,MACvE;AACA,eAAS,OAAO,KAAK;AACnB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS,GAAG;AAAA,MACtE;AACA,YAAM,MAAS;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AACA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,WAAY,iBAAgB,UAAU,UAAU;AACtD;AACA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASI,iBAAgBC,IAAG;AACnG,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBD,IAAGE,IAAG;AACtG,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AACA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AACjE,MAAI,QAAQ,UAAU,KAAM,QAAO;AACnC,MAAI,OAAO,UAAU,WAAY,QAAO;AACxC,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,SAAS,WAAW,QAAQ,MAAM,OAAO;AACvC,MAAI,0BAA0B,GAAG;AAC/B,iBAAa,QAAQ,UAAU,KAAK;AAAA,EACtC,OAAO;AACL,iBAAa,SAASG,YAAWC,SAAQC,OAAMC,QAAO;AACpD,UAAI,IAAI,CAAC,IAAI;AACb,QAAE,KAAK,MAAM,GAAGD,KAAI;AACpB,UAAI,cAAc,SAAS,KAAK,MAAMD,SAAQ,CAAC;AAC/C,UAAI,WAAW,IAAI,YAAY;AAC/B,UAAIE,OAAO,iBAAgB,UAAUA,OAAM,SAAS;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AACA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,SAAS,SAAS,KAAK,EAAE,EAAE,QAAQ,eAAe,MAAM;AACjE;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,SAAS,OAAO,QAAQ,aAAa,oBAAI,IAAI,IAAI;AACrD,qBAAmB,SAASC,kBAAiBD,QAAO;AAClD,QAAIA,WAAU,QAAQ,CAAC,kBAAkBA,MAAK,EAAG,QAAOA;AACxD,QAAI,OAAOA,WAAU,YAAY;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC1E;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,OAAO,IAAIA,MAAK,EAAG,QAAO,OAAO,IAAIA,MAAK;AAC9C,aAAO,IAAIA,QAAO,OAAO;AAAA,IAC3B;AACA,aAAS,UAAU;AACjB,aAAO,WAAWA,QAAO,WAAW,gBAAgB,IAAI,EAAE,WAAW;AAAA,IACvE;AACA,YAAQ,YAAY,OAAO,OAAOA,OAAM,WAAW;AAAA,MACjD,aAAa;AAAA,QACX,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,gBAAgB,SAASA,MAAK;AAAA,EACvC;AACA,SAAO,iBAAiB,KAAK;AAC/B;AACA,SAAS,uBAAuBX,OAAM;AACpC,MAAIA,UAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AACA,SAAOA;AACT;AACA,SAAS,2BAA2BA,OAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AACA,SAAO,uBAAuBA,KAAI;AACpC;AACA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAC1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GACjC;AACF,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AACtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AACA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AACA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAW;AACtB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;AAWA,IAAI,SAAS,OAAO,WAAW,eAAe,CAAC,EAAE,SAAS,KAAK,MAAM,MAAM;AAS3E,SAAS,cAAc,MAAM,MAAM;AACjC,MAAI,KAAK,QAAQ,KAAK,YAAY,CAAC,MAAM,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,SAAO,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,OAAO,KAAK,OAAO,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AACpG;AAQA,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,WAAW,QAAQ,aAAa,KAAK,cAAc,WAAW,QAAQ,iBAAiB,QAAQ,cAAc,WAAW;AACzI;AAUA,SAAS,UAAU,OAAO;AAExB,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAC9E;AAQA,SAAS,WAAW,KAAK;AACvB,SAAO,oHAAoH,KAAK,GAAG;AACrI;AAQA,SAAS,aAAa,KAAK;AACzB,MAAI,OAAO;AACX,SAAO,KAAK,KAAK,GAAG;AACtB;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,SAAS,OAAO,IAAI,MAAM,gCAAgC;AAC9D,MAAI,UAAU,SAAS,MAAM,CAAC,KAAK,IAAI,QAAQ,WAAW,EAAE;AAC5D,MAAI,gBAAgB,CAAC,eAAe,eAAe,aAAa;AAChE,WAAS,KAAK,GAAG,iBAAiB,eAAe,KAAK,eAAe,QAAQ,MAAM;AACjF,QAAI,eAAe,eAAe,EAAE;AACpC,QAAI,OAAO,SAAS,YAAY,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AASA,SAAS,cAAc;AACrB,MAAIa,oBAAmB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC5F,MAAI,KAAKA,kBAAiB;AAC1B,MAAI,MAAMA,kBAAiB;AAC3B,MAAI,UAAU,MAAM;AACpB,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,6GAA6G;AAAA,EAC/H;AACA,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO,qBAAqB,OAAO,OAAO;AAAA,EAC5C;AACA,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO,QAAQ,QAAQ,SAAS,QAAQ;AAAA,EAC1C;AACA,MAAI,IAAI;AACN,UAAM,IAAI,UAAU,IAAS,OAAO,IAAI,4BAAiC,CAAC;AAAA,EAC5E;AACA,QAAM,IAAI,UAAU,IAAS,OAAO,SAAS,2BAAgC,CAAC;AAChF;AAaA,IAAI,YAAY,SAASC,WAAU,QAAQ,WAAW,UAAU;AAC9D,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,MAAI,aAAa,OAAO,cAAc,WAAW,CAAC,SAAS,IAAI;AAC/D,aAAW,QAAQ,SAAU,QAAQ;AACnC,WAAO,MAAM,EAAE,QAAQ,QAAQ;AAAA,EACjC,CAAC;AACD,SAAO;AAAA,IACL,QAAQ,SAAS,SAAS;AACxB,aAAO,WAAW,QAAQ,SAAU,QAAQ;AAC1C,eAAO,OAAO,OAAO,EAAE,QAAQ,QAAQ;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAI,sBAAsB,OAAO,MAAM,UAAU,YAAY;AAC7D,IAAI,qBAAqB,OAAO,WAAW,eAAe,OAAO,OAAO,gBAAgB;AACxF,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,qBAAqB;AAC5D,QAAM,IAAI,MAAM,+DAA+D;AACjF;AAEA,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAE9L,SAAS,qBAAqB,IAAI,QAAQ;AACzC,SAAO,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACrE;AAAA,CASC,SAAUd,OAAM;AAEf,MAAIA,MAAK,SAAS;AAChB;AAAA,EACF;AACA,MAAI,iBAAiB,OAAO,UAAU;AACtC,MAAI,YAAY,OAAO,kBAAkB,WAAY;AACnD,QAAI;AAEF,aAAO,OAAO,eAAe,CAAC,GAAG,KAAK;AAAA,QACpC,OAAO;AAAA,MACT,CAAC,EAAE,MAAM;AAAA,IACX,SAAS,GAAG;AAAA,IAAC;AAAA,EACf,EAAE;AACF,MAAI,iBAAiB,SAAU,QAAQ,MAAM,OAAO;AAClD,QAAI,WAAW;AACb,aAAO,eAAe,QAAQ,MAAM;AAAA,QAClC,cAAc;AAAA,QACd,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO,IAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACA,EAAAA,MAAK,UAAU,WAAY;AAEzB,aAASe,WAAU;AACjB,UAAI,SAAS,QAAQ;AACnB,cAAM,IAAI,UAAU,oCAAoC;AAAA,MAC1D;AACA,qBAAe,MAAM,OAAO,MAAM,UAAU,CAAC;AAG7C,UAAI,UAAU,SAAS,GAAG;AAExB,cAAM,IAAI,UAAU,mCAAmC;AAAA,MACzD;AAAA,IACF;AAGA,mBAAeA,SAAQ,WAAW,UAAU,SAAU,KAAK;AACzD,oBAAc,MAAM,QAAQ;AAC5B,UAAI,CAAC,SAAS,GAAG,GAAG;AAClB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,KAAK,GAAG;AACxB,UAAI,SAAS,MAAM,CAAC,MAAM,KAAK;AAC7B,eAAO,IAAI,KAAK,GAAG;AACnB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAGD,mBAAeA,SAAQ,WAAW,OAAO,SAAU,KAAK;AACtD,oBAAc,MAAM,KAAK;AACzB,UAAI,CAAC,SAAS,GAAG,GAAG;AAClB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,KAAK,GAAG;AACxB,UAAI,SAAS,MAAM,CAAC,MAAM,KAAK;AAC7B,eAAO,MAAM,CAAC;AAAA,MAChB;AACA,aAAO;AAAA,IACT,CAAC;AAGD,mBAAeA,SAAQ,WAAW,OAAO,SAAU,KAAK;AACtD,oBAAc,MAAM,KAAK;AACzB,UAAI,CAAC,SAAS,GAAG,GAAG;AAClB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,KAAK,GAAG;AACxB,UAAI,SAAS,MAAM,CAAC,MAAM,KAAK;AAC7B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAGD,mBAAeA,SAAQ,WAAW,OAAO,SAAU,KAAK,OAAO;AAC7D,oBAAc,MAAM,KAAK;AACzB,UAAI,CAAC,SAAS,GAAG,GAAG;AAClB,cAAM,IAAI,UAAU,oCAAoC;AAAA,MAC1D;AACA,UAAI,QAAQ,IAAI,KAAK,GAAG;AACxB,UAAI,SAAS,MAAM,CAAC,MAAM,KAAK;AAC7B,cAAM,CAAC,IAAI;AACX,eAAO;AAAA,MACT;AACA,qBAAe,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAC1C,aAAO;AAAA,IACT,CAAC;AACD,aAAS,cAAc,GAAG,YAAY;AACpC,UAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,KAAK,GAAG,KAAK,GAAG;AAClD,cAAM,IAAI,UAAU,aAAa,6CAA6C,OAAO,CAAC;AAAA,MACxF;AAAA,IACF;AACA,aAAS,MAAM,QAAQ;AACrB,aAAO,SAAS,MAAM,KAAK,IAAI,MAAM,KAAK;AAAA,IAC5C;AACA,aAAS,OAAO;AACd,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;AAAA,IAC7C;AACA,mBAAeA,UAAS,aAAa,IAAI;AACzC,WAAOA;AAAA,EACT,EAAE;AACF,WAAS,SAAS,GAAG;AACnB,WAAO,OAAO,CAAC,MAAM;AAAA,EACvB;AACF,GAAG,OAAO,eAAe,cAAc,aAAa,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,OAAO,mBAAmB,cAAc,iBAAiB,cAAc;AAEzM,IAAI,UAAU,qBAAqB,SAAU,QAAQ;AAMrD,GAAC,SAAS,IAAI,MAAM,SAAS,YAAY;AAEvC,YAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,WAAW;AAC5C,QAAK,OAAO,SAAS;AACnB,aAAO,UAAU,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACF,GAAG,WAAW,OAAO,kBAAkB,cAAc,iBAAiB,gBAAgB,SAAS,MAAM;AAEnG,QAAI,aACF,OACA,kBACA,WAAW,OAAO,UAAU,UAC5B,QAAQ,OAAO,gBAAgB,cAAc,SAASC,OAAM,IAAI;AAC9D,aAAO,aAAa,EAAE;AAAA,IACxB,IAAI;AAGN,QAAI;AACF,aAAO,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC;AACjC,oBAAc,SAASC,aAAY,KAAK,MAAM,KAAK,QAAQ;AACzD,eAAO,OAAO,eAAe,KAAK,MAAM;AAAA,UACtC,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc,WAAW;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF,SAAS,KAAK;AACZ,oBAAc,SAASA,aAAY,KAAK,MAAM,KAAK;AACjD,YAAI,IAAI,IAAI;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAGA,uBAAmB,yBAAS,QAAQ;AAClC,UAAI,OAAO,MAAM;AACjB,eAAS,KAAK,IAAIjB,OAAM;AACtB,aAAK,KAAK;AACV,aAAK,OAAOA;AACZ,aAAK,OAAO;AAAA,MACd;AACA,aAAO;AAAA,QACL,KAAK,SAAS,IAAI,IAAIA,OAAM;AAC1B,iBAAO,IAAI,KAAK,IAAIA,KAAI;AACxB,cAAI,MAAM;AACR,iBAAK,OAAO;AAAA,UACd,OAAO;AACL,oBAAQ;AAAA,UACV;AACA,iBAAO;AACP,iBAAO;AAAA,QACT;AAAA,QACA,OAAO,SAAS,QAAQ;AACtB,cAAI,IAAI;AACR,kBAAQ,OAAO,QAAQ;AACvB,iBAAO,GAAG;AACR,cAAE,GAAG,KAAK,EAAE,IAAI;AAChB,gBAAI,EAAE;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE;AACF,aAAS,SAAS,IAAIA,OAAM;AAC1B,uBAAiB,IAAI,IAAIA,KAAI;AAC7B,UAAI,CAAC,OAAO;AACV,gBAAQ,MAAM,iBAAiB,KAAK;AAAA,MACtC;AAAA,IACF;AAGA,aAAS,WAAW,GAAG;AACrB,UAAI,OACF,SAAS,OAAO;AAClB,UAAI,KAAK,SAAS,UAAU,YAAY,UAAU,aAAa;AAC7D,gBAAQ,EAAE;AAAA,MACZ;AACA,aAAO,OAAO,SAAS,aAAa,QAAQ;AAAA,IAC9C;AACA,aAAS,SAAS;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,uBAAe,MAAM,KAAK,UAAU,IAAI,KAAK,MAAM,CAAC,EAAE,UAAU,KAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC,CAAC;AAAA,MACtG;AACA,WAAK,MAAM,SAAS;AAAA,IACtB;AAKA,aAAS,eAAeA,OAAM,IAAI,OAAO;AACvC,UAAI,KAAK;AACT,UAAI;AACF,YAAI,OAAO,OAAO;AAChB,gBAAM,OAAOA,MAAK,GAAG;AAAA,QACvB,OAAO;AACL,cAAI,OAAO,MAAM;AACf,kBAAMA,MAAK;AAAA,UACb,OAAO;AACL,kBAAM,GAAG,KAAK,QAAQA,MAAK,GAAG;AAAA,UAChC;AACA,cAAI,QAAQ,MAAM,SAAS;AACzB,kBAAM,OAAO,UAAU,qBAAqB,CAAC;AAAA,UAC/C,WAAW,QAAQ,WAAW,GAAG,GAAG;AAClC,kBAAM,KAAK,KAAK,MAAM,SAAS,MAAM,MAAM;AAAA,UAC7C,OAAO;AACL,kBAAM,QAAQ,GAAG;AAAA,UACnB;AAAA,QACF;AAAA,MACF,SAAS,KAAK;AACZ,cAAM,OAAO,GAAG;AAAA,MAClB;AAAA,IACF;AACA,aAAS,QAAQ,KAAK;AACpB,UAAI,OACFA,QAAO;AAGT,UAAIA,MAAK,WAAW;AAClB;AAAA,MACF;AACA,MAAAA,MAAK,YAAY;AAGjB,UAAIA,MAAK,KAAK;AACZ,QAAAA,QAAOA,MAAK;AAAA,MACd;AACA,UAAI;AACF,YAAI,QAAQ,WAAW,GAAG,GAAG;AAC3B,mBAAS,WAAY;AACnB,gBAAI,cAAc,IAAI,eAAeA,KAAI;AACzC,gBAAI;AACF,oBAAM,KAAK,KAAK,SAAS,YAAY;AACnC,wBAAQ,MAAM,aAAa,SAAS;AAAA,cACtC,GAAG,SAAS,WAAW;AACrB,uBAAO,MAAM,aAAa,SAAS;AAAA,cACrC,CAAC;AAAA,YACH,SAAS,KAAK;AACZ,qBAAO,KAAK,aAAa,GAAG;AAAA,YAC9B;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,UAAAA,MAAK,MAAM;AACX,UAAAA,MAAK,QAAQ;AACb,cAAIA,MAAK,MAAM,SAAS,GAAG;AACzB,qBAAS,QAAQA,KAAI;AAAA,UACvB;AAAA,QACF;AAAA,MACF,SAAS,KAAK;AACZ,eAAO,KAAK,IAAI,eAAeA,KAAI,GAAG,GAAG;AAAA,MAC3C;AAAA,IACF;AACA,aAAS,OAAO,KAAK;AACnB,UAAIA,QAAO;AAGX,UAAIA,MAAK,WAAW;AAClB;AAAA,MACF;AACA,MAAAA,MAAK,YAAY;AAGjB,UAAIA,MAAK,KAAK;AACZ,QAAAA,QAAOA,MAAK;AAAA,MACd;AACA,MAAAA,MAAK,MAAM;AACX,MAAAA,MAAK,QAAQ;AACb,UAAIA,MAAK,MAAM,SAAS,GAAG;AACzB,iBAAS,QAAQA,KAAI;AAAA,MACvB;AAAA,IACF;AACA,aAAS,gBAAgB,aAAa,KAAK,UAAU,UAAU;AAC7D,eAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO;AACzC,SAAC,SAAS,KAAKkB,MAAK;AAClB,sBAAY,QAAQ,IAAIA,IAAG,CAAC,EAAE,KAAK,SAAS,WAAW,KAAK;AAC1D,qBAASA,MAAK,GAAG;AAAA,UACnB,GAAG,QAAQ;AAAA,QACb,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AACA,aAAS,eAAelB,OAAM;AAC5B,WAAK,MAAMA;AACX,WAAK,YAAY;AAAA,IACnB;AACA,aAAS,QAAQA,OAAM;AACrB,WAAK,UAAUA;AACf,WAAK,QAAQ;AACb,WAAK,YAAY;AACjB,WAAK,QAAQ,CAAC;AACd,WAAK,MAAM;AAAA,IACb;AACA,aAASmB,SAAQ,UAAU;AACzB,UAAI,OAAO,YAAY,YAAY;AACjC,cAAM,UAAU,gBAAgB;AAAA,MAClC;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,cAAM,UAAU,eAAe;AAAA,MACjC;AAIA,WAAK,UAAU;AACf,UAAI,MAAM,IAAI,QAAQ,IAAI;AAC1B,WAAK,MAAM,IAAI,SAAS,KAAK,SAAS,SAAS;AAC7C,YAAI,IAAI;AAAA,UACN,SAAS,OAAO,WAAW,aAAa,UAAU;AAAA,UAClD,SAAS,OAAO,WAAW,aAAa,UAAU;AAAA,QACpD;AAIA,UAAE,UAAU,IAAI,KAAK,YAAY,SAAS,aAAaC,UAASC,SAAQ;AACtE,cAAI,OAAOD,YAAW,cAAc,OAAOC,WAAU,YAAY;AAC/D,kBAAM,UAAU,gBAAgB;AAAA,UAClC;AACA,YAAE,UAAUD;AACZ,YAAE,SAASC;AAAA,QACb,CAAC;AACD,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,IAAI,UAAU,GAAG;AACnB,mBAAS,QAAQ,GAAG;AAAA,QACtB;AACA,eAAO,EAAE;AAAA,MACX;AACA,WAAK,OAAO,IAAI,SAAS,QAAQ,SAAS;AACxC,eAAO,KAAK,KAAK,QAAQ,OAAO;AAAA,MAClC;AACA,UAAI;AACF,iBAAS,KAAK,QAAQ,SAAS,cAAc,KAAK;AAChD,kBAAQ,KAAK,KAAK,GAAG;AAAA,QACvB,GAAG,SAAS,aAAa,KAAK;AAC5B,iBAAO,KAAK,KAAK,GAAG;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,KAAK,GAAG;AAAA,MACtB;AAAA,IACF;AACA,QAAI,mBAAmB;AAAA,MAAY,CAAC;AAAA,MAAG;AAAA,MAAeF;AAAA;AAAA,MAA0B;AAAA,IAAK;AAGrF,IAAAA,SAAQ,YAAY;AAGpB;AAAA,MAAY;AAAA,MAAkB;AAAA,MAAW;AAAA;AAAA,MAAoB;AAAA,IAAK;AAClE,gBAAYA,UAAS,WAAW,SAAS,gBAAgB,KAAK;AAC5D,UAAI,cAAc;AAIlB,UAAI,OAAO,OAAO,OAAO,YAAY,IAAI,YAAY,GAAG;AACtD,eAAO;AAAA,MACT;AACA,aAAO,IAAI,YAAY,SAAS,SAASC,UAASC,SAAQ;AACxD,YAAI,OAAOD,YAAW,cAAc,OAAOC,WAAU,YAAY;AAC/D,gBAAM,UAAU,gBAAgB;AAAA,QAClC;AACA,QAAAD,SAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACD,gBAAYD,UAAS,UAAU,SAAS,eAAe,KAAK;AAC1D,aAAO,IAAI,KAAK,SAAS,SAASC,UAASC,SAAQ;AACjD,YAAI,OAAOD,YAAW,cAAc,OAAOC,WAAU,YAAY;AAC/D,gBAAM,UAAU,gBAAgB;AAAA,QAClC;AACA,QAAAA,QAAO,GAAG;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AACD,gBAAYF,UAAS,OAAO,SAAS,YAAY,KAAK;AACpD,UAAI,cAAc;AAGlB,UAAI,SAAS,KAAK,GAAG,KAAK,kBAAkB;AAC1C,eAAO,YAAY,OAAO,UAAU,cAAc,CAAC;AAAA,MACrD;AACA,UAAI,IAAI,WAAW,GAAG;AACpB,eAAO,YAAY,QAAQ,CAAC,CAAC;AAAA,MAC/B;AACA,aAAO,IAAI,YAAY,SAAS,SAASC,UAASC,SAAQ;AACxD,YAAI,OAAOD,YAAW,cAAc,OAAOC,WAAU,YAAY;AAC/D,gBAAM,UAAU,gBAAgB;AAAA,QAClC;AACA,YAAI,MAAM,IAAI,QACZ,OAAO,MAAM,GAAG,GAChB,QAAQ;AACV,wBAAgB,aAAa,KAAK,SAAS,SAAS,KAAK,KAAK;AAC5D,eAAK,GAAG,IAAI;AACZ,cAAI,EAAE,UAAU,KAAK;AACnB,YAAAD,SAAQ,IAAI;AAAA,UACd;AAAA,QACF,GAAGC,OAAM;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AACD,gBAAYF,UAAS,QAAQ,SAAS,aAAa,KAAK;AACtD,UAAI,cAAc;AAGlB,UAAI,SAAS,KAAK,GAAG,KAAK,kBAAkB;AAC1C,eAAO,YAAY,OAAO,UAAU,cAAc,CAAC;AAAA,MACrD;AACA,aAAO,IAAI,YAAY,SAAS,SAASC,UAASC,SAAQ;AACxD,YAAI,OAAOD,YAAW,cAAc,OAAOC,WAAU,YAAY;AAC/D,gBAAM,UAAU,gBAAgB;AAAA,QAClC;AACA,wBAAgB,aAAa,KAAK,SAAS,SAAS,KAAK,KAAK;AAC5D,UAAAD,SAAQ,GAAG;AAAA,QACb,GAAGC,OAAM;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AACD,WAAOF;AAAA,EACT,CAAC;AACD,CAAC;AAMD,IAAI,cAAc,oBAAI,QAAQ;AAW9B,SAAS,cAAc,QAAQ,MAAM,UAAU;AAC7C,MAAI,kBAAkB,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC;AAC1D,MAAI,EAAE,QAAQ,kBAAkB;AAC9B,oBAAgB,IAAI,IAAI,CAAC;AAAA,EAC3B;AACA,kBAAgB,IAAI,EAAE,KAAK,QAAQ;AACnC,cAAY,IAAI,OAAO,SAAS,eAAe;AACjD;AASA,SAAS,aAAa,QAAQ,MAAM;AAClC,MAAI,kBAAkB,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC;AAC1D,SAAO,gBAAgB,IAAI,KAAK,CAAC;AACnC;AAUA,SAAS,eAAe,QAAQ,MAAM,UAAU;AAC9C,MAAI,kBAAkB,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC;AAC1D,MAAI,CAAC,gBAAgB,IAAI,GAAG;AAC1B,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,UAAU;AACb,oBAAgB,IAAI,IAAI,CAAC;AACzB,gBAAY,IAAI,OAAO,SAAS,eAAe;AAC/C,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,gBAAgB,IAAI,EAAE,QAAQ,QAAQ;AAClD,MAAI,UAAU,IAAI;AAChB,oBAAgB,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,EACvC;AACA,cAAY,IAAI,OAAO,SAAS,eAAe;AAC/C,SAAO,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,EAAE,WAAW;AACnE;AASA,SAAS,eAAe,QAAQ,MAAM;AACpC,MAAI,kBAAkB,aAAa,QAAQ,IAAI;AAC/C,MAAI,gBAAgB,SAAS,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,WAAW,gBAAgB,MAAM;AACrC,iBAAe,QAAQ,MAAM,QAAQ;AACrC,SAAO;AACT;AASA,SAAS,cAAc,YAAY,YAAY;AAC7C,MAAI,kBAAkB,YAAY,IAAI,UAAU;AAChD,cAAY,IAAI,YAAY,eAAe;AAC3C,cAAY,OAAO,UAAU;AAC/B;AAYA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI;AACF,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB,SAAS,OAAO;AAEd,cAAQ,KAAK,KAAK;AAClB,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,YAAY,QAAQ,QAAQ,QAAQ;AAC3C,MAAI,CAAC,OAAO,QAAQ,iBAAiB,CAAC,OAAO,QAAQ,cAAc,aAAa;AAC9E;AAAA,EACF;AACA,MAAI,UAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,WAAW,QAAW;AACxB,YAAQ,QAAQ;AAAA,EAClB;AAGA,MAAI,YAAY,WAAW,UAAU,UAAU,YAAY,EAAE,QAAQ,oBAAoB,IAAI,CAAC;AAC9F,MAAI,aAAa,KAAK,YAAY,IAAI;AACpC,cAAU,KAAK,UAAU,OAAO;AAAA,EAClC;AACA,SAAO,QAAQ,cAAc,YAAY,SAAS,OAAO,MAAM;AACjE;AASA,SAAS,YAAY,QAAQ,MAAM;AACjC,SAAO,iBAAiB,IAAI;AAC5B,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,MAAI,KAAK,OAAO;AACd,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,WAAW,aAAa,QAAQ,KAAK,KAAK,MAAM;AACpD,eAAS,QAAQ,SAAU,SAAS;AAClC,YAAI,QAAQ,IAAI,MAAM,KAAK,KAAK,OAAO;AACvC,cAAM,OAAO,KAAK,KAAK;AACvB,gBAAQ,OAAO,KAAK;AACpB,uBAAe,QAAQ,KAAK,KAAK,QAAQ,OAAO;AAAA,MAClD,CAAC;AAAA,IACH;AACA,gBAAY,aAAa,QAAQ,SAAS,OAAO,KAAK,KAAK,CAAC;AAC5D,YAAQ,KAAK;AAAA,EACf,WAAW,KAAK,QAAQ;AACtB,QAAI,WAAW,eAAe,QAAQ,KAAK,MAAM;AACjD,QAAI,UAAU;AACZ,gBAAU,KAAK,QAAQ;AACvB,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,YAAU,QAAQ,SAAUG,WAAU;AACpC,QAAI;AACF,UAAI,OAAOA,cAAa,YAAY;AAClC,QAAAA,UAAS,KAAK,QAAQ,KAAK;AAC3B;AAAA,MACF;AACA,MAAAA,UAAS,QAAQ,KAAK;AAAA,IACxB,SAAS,GAAG;AAAA,IAEZ;AAAA,EACF,CAAC;AACH;AAKA,IAAI,mBAAmB,CAAC,WAAW,gBAAgB,cAAc,aAAa,YAAY,cAAc,UAAU,MAAM,cAAc,YAAY,cAAc,SAAS,UAAU,YAAY,OAAO,YAAY,cAAc,UAAU,MAAM,mBAAmB,sBAAsB,YAAY,QAAQ,aAAa,eAAe,YAAY,eAAe,SAAS,wBAAwB,eAAe,YAAY,WAAW,gBAAgB,WAAW,oBAAoB,cAAc,oBAAoB,SAAS,cAAc,aAAa,gBAAgB,SAAS,cAAc,eAAe,iBAAiB,OAAO,cAAc,UAAU,oBAAoB,OAAO;AASjqB,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACpF,SAAO,iBAAiB,OAAO,SAAU,QAAQ,OAAO;AACtD,QAAI,QAAQ,QAAQ,aAAa,cAAc,OAAO,KAAK,CAAC;AAC5D,QAAI,SAAS,UAAU,IAAI;AACzB,aAAO,KAAK,IAAI,UAAU,KAAK,IAAI;AAAA,IACrC;AACA,WAAO;AAAA,EACT,GAAG,QAAQ;AACb;AASA,SAAS,YAAY,MAAM,SAAS;AAClC,MAAI,OAAO,KAAK;AAChB,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,UAAU,6BAA6B;AAAA,EACnD;AACA,MAAI,QAAQ,aAAa,wBAAwB,MAAM,MAAM;AAC3D,WAAO,QAAQ,cAAc,QAAQ;AAAA,EACvC;AACA,MAAI,MAAM,SAAS,cAAc,KAAK;AACtC,MAAI,YAAY;AAChB,UAAQ,YAAY,IAAI,UAAU;AAClC,UAAQ,aAAa,0BAA0B,MAAM;AACrD,SAAO,QAAQ,cAAc,QAAQ;AACvC;AAUA,SAAS,cAAc,UAAU;AAC/B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,UAAU,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACpD,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,CAAC,WAAW,QAAQ,GAAG;AACzB,YAAM,IAAI,UAAU,IAAS,OAAO,UAAU,2BAAgC,CAAC;AAAA,IACjF;AACA,QAAI,SAAS,gBAAgB,QAAQ;AACrC,QAAI,MAAM,WAAW,OAAO,QAAQ,uBAAuB,EAAE,OAAO,mBAAmB,QAAQ,CAAC;AAChG,aAAS,SAAS,QAAQ;AACxB,UAAI,OAAO,eAAe,KAAK,GAAG;AAChC,eAAO,IAAI,OAAO,OAAO,GAAG,EAAE,OAAO,mBAAmB,OAAO,KAAK,CAAC,CAAC;AAAA,MACxE;AAAA,IACF;AACA,QAAI,MAAM,oBAAoB,SAAS,IAAI,eAAe,IAAI,IAAI,eAAe;AACjF,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,SAAS,WAAY;AACvB,UAAI,IAAI,WAAW,KAAK;AACtB,eAAO,IAAI,MAAM,IAAS,OAAO,UAAU,kBAAuB,CAAC,CAAC;AACpE;AAAA,MACF;AACA,UAAI,IAAI,WAAW,KAAK;AACtB,eAAO,IAAI,MAAM,IAAS,OAAO,UAAU,sBAA2B,CAAC,CAAC;AACxE;AAAA,MACF;AACA,UAAI;AACF,YAAI,OAAO,KAAK,MAAM,IAAI,YAAY;AAEtC,YAAI,KAAK,uBAAuB,KAAK;AAEnC,sBAAY,MAAM,OAAO;AACzB,iBAAO,IAAI,MAAM,IAAS,OAAO,UAAU,sBAA2B,CAAC,CAAC;AACxE;AAAA,QACF;AACA,gBAAQ,IAAI;AAAA,MACd,SAAS,OAAO;AACd,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,QAAI,UAAU,WAAY;AACxB,UAAI,SAAS,IAAI,SAAS,KAAK,OAAO,IAAI,QAAQ,GAAG,IAAI;AACzD,aAAO,IAAI,MAAM,wDAAwD,OAAO,QAAQ,GAAG,CAAC,CAAC;AAAA,IAC/F;AACA,QAAI,KAAK;AAAA,EACX,CAAC;AACH;AAQA,SAAS,mBAAmB;AAC1B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAI,WAAW,CAAC,EAAE,MAAM,KAAK,OAAO,iBAAiB,mCAAmC,CAAC;AACzF,MAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,QAAI,aAAa,UAAU,QAAQ,OAAO;AACxC,cAAQ,MAAM,yCAAyC,OAAO,KAAK,CAAC;AAAA,IACtE;AAAA,EACF;AACA,WAAS,QAAQ,SAAU,SAAS;AAClC,QAAI;AAEF,UAAI,QAAQ,aAAa,kBAAkB,MAAM,MAAM;AACrD;AAAA,MACF;AACA,UAAI,SAAS,oBAAoB,OAAO;AACxC,UAAI,MAAM,YAAY,MAAM;AAC5B,oBAAc,KAAK,QAAQ,OAAO,EAAE,KAAK,SAAU,MAAM;AACvD,eAAO,YAAY,MAAM,OAAO;AAAA,MAClC,CAAC,EAAE,MAAM,WAAW;AAAA,IACtB,SAAS,OAAO;AACd,kBAAY,KAAK;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAQA,SAAS,eAAe;AACtB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,MAAI,OAAO,0BAA0B;AACnC;AAAA,EACF;AACA,SAAO,2BAA2B;AAClC,MAAI,YAAY,SAASC,WAAU,OAAO;AACxC,QAAI,CAAC,WAAW,MAAM,MAAM,GAAG;AAC7B;AAAA,IACF;AAGA,QAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,UAAU,eAAe;AACrD;AAAA,IACF;AACA,QAAI,UAAU,OAAO,iBAAiB,QAAQ;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,QAAQ,CAAC,EAAE,kBAAkB,MAAM,QAAQ;AAC7C;AAAA,MACF;AAIA,UAAI,QAAQ,QAAQ,CAAC,EAAE;AACvB,YAAM,MAAM,gBAAgB,GAAG,OAAO,MAAM,KAAK,KAAK,CAAC,EAAE,QAAQ,IAAI;AACrE;AAAA,IACF;AAAA,EACF;AACA,SAAO,iBAAiB,WAAW,SAAS;AAC9C;AAQA,SAAS,0BAA0B;AACjC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,MAAI,OAAO,0BAA0B;AACnC;AAAA,EACF;AACA,SAAO,2BAA2B;AAClC,MAAI,YAAY,SAASA,WAAU,OAAO;AACxC,QAAI,CAAC,WAAW,MAAM,MAAM,GAAG;AAC7B;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,MAAM,IAAI;AACtC,QAAI,CAAC,QAAQ,KAAK,UAAU,SAAS;AACnC;AAAA,IACF;AACA,QAAI,UAAU,OAAO,iBAAiB,QAAQ;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,SAAS,QAAQ,CAAC;AAGtB,UAAI,uBAAuB,OAAO,kBAAkB,MAAM;AAC1D,UAAI,aAAa,OAAO,GAAG,KAAK,sBAAsB;AACpD,YAAI,SAAS,IAAI,OAAO,MAAM;AAC9B,eAAO,WAAW,uBAAuB,OAAO,SAAS,IAAI;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACA,SAAO,iBAAiB,WAAW,SAAS;AAC9C;AAQA,SAAS,oBAAoB;AAC3B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,MAAI,OAAO,0BAA0B;AACnC;AAAA,EACF;AACA,SAAO,2BAA2B;AAClC,MAAI,cAAc,SAASD,aAAY,OAAO;AAC5C,QAAI,aAAa,UAAU,QAAQ,OAAO;AACxC,cAAQ,MAAM,wCAAwC,OAAO,KAAK,CAAC;AAAA,IACrE;AAAA,EACF;AACA,MAAI,YAAY,SAASC,WAAU,OAAO;AACxC,QAAI,CAAC,WAAW,MAAM,MAAM,GAAG;AAC7B;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,MAAM,IAAI;AACtC,QAAI,CAAC,QAAQ,KAAK,UAAU,SAAS;AACnC;AAAA,IACF;AACA,QAAI,UAAU,OAAO,iBAAiB,QAAQ;AAC9C,QAAI,QAAQ,SAASC,SAAQ;AAC3B,UAAI,SAAS,QAAQ,CAAC;AACtB,UAAI,uBAAuB,OAAO,kBAAkB,MAAM;AAC1D,UAAI,aAAa,OAAO,GAAG,KAAK,sBAAsB;AACpD,YAAI,SAAS,IAAI,OAAO,MAAM;AAC9B,eAAO,WAAW,EAAE,KAAK,SAAU,SAAS;AAC1C,cAAI,UAAU,IAAI,OAAO,eAAe,OAAO,SAAS,WAAW,CAAC,EAAE,KAAK,OAAO,SAAS,IAAI;AAC/F,cAAI,WAAW,QAAQ,CAAC,GAAG;AACzB,gBAAI,MAAM,UAAU,QAAQ,CAAC,CAAC;AAC9B,mBAAO,eAAe,GAAG;AAAA,UAC3B;AACA;AAAA,QACF,CAAC,EAAE,MAAM,WAAW;AAAA,MACtB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,iBAAiB,WAAW,SAAS;AAC9C;AAaA,SAAS,uBAAuB;AAC9B,MAAI,KAAK,WAAY;AACnB,QAAI;AACJ,QAAI,QAAQ;AAAA,MAAC,CAAC,qBAAqB,kBAAkB,qBAAqB,qBAAqB,oBAAoB,iBAAiB;AAAA;AAAA,MAEpI,CAAC,2BAA2B,wBAAwB,2BAA2B,2BAA2B,0BAA0B,uBAAuB;AAAA;AAAA,MAE3J,CAAC,2BAA2B,0BAA0B,kCAAkC,0BAA0B,0BAA0B,uBAAuB;AAAA,MAAG,CAAC,wBAAwB,uBAAuB,wBAAwB,wBAAwB,uBAAuB,oBAAoB;AAAA,MAAG,CAAC,uBAAuB,oBAAoB,uBAAuB,uBAAuB,sBAAsB,mBAAmB;AAAA,IAAC;AACxb,QAAI,IAAI;AACR,QAAI,IAAI,MAAM;AACd,QAAI,MAAM,CAAC;AACX,WAAO,IAAI,GAAG,KAAK;AACjB,YAAM,MAAM,CAAC;AACb,UAAI,OAAO,IAAI,CAAC,KAAK,UAAU;AAC7B,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,cAAI,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,EAAE;AACF,MAAI,eAAe;AAAA,IACjB,kBAAkB,GAAG;AAAA,IACrB,iBAAiB,GAAG;AAAA,EACtB;AACA,MAAIC,cAAa;AAAA,IACf,SAAS,SAAS,QAAQ,SAAS;AACjC,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,sBAAsB,SAASC,uBAAsB;AACvD,UAAAD,YAAW,IAAI,oBAAoBC,oBAAmB;AACtD,kBAAQ;AAAA,QACV;AACA,QAAAD,YAAW,GAAG,oBAAoB,mBAAmB;AACrD,kBAAU,WAAW,SAAS;AAC9B,YAAI,gBAAgB,QAAQ,GAAG,iBAAiB,EAAE;AAClD,YAAI,yBAAyB,SAAS;AACpC,wBAAc,KAAK,mBAAmB,EAAE,MAAM,MAAM;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,MAAM,SAAS,OAAO;AACpB,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,CAACA,YAAW,cAAc;AAC5B,kBAAQ;AACR;AAAA,QACF;AACA,YAAI,mBAAmB,SAASE,oBAAmB;AACjD,UAAAF,YAAW,IAAI,oBAAoBE,iBAAgB;AACnD,kBAAQ;AAAA,QACV;AACA,QAAAF,YAAW,GAAG,oBAAoB,gBAAgB;AAClD,YAAI,gBAAgB,SAAS,GAAG,cAAc,EAAE;AAChD,YAAI,yBAAyB,SAAS;AACpC,wBAAc,KAAK,gBAAgB,EAAE,MAAM,MAAM;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,IAAI,SAAS,GAAG,OAAO,UAAU;AAC/B,UAAI,YAAY,aAAa,KAAK;AAClC,UAAI,WAAW;AACb,iBAAS,iBAAiB,WAAW,QAAQ;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,KAAK,SAAS,IAAI,OAAO,UAAU;AACjC,UAAI,YAAY,aAAa,KAAK;AAClC,UAAI,WAAW;AACb,iBAAS,oBAAoB,WAAW,QAAQ;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AACA,SAAO,iBAAiBA,aAAY;AAAA,IAClC,cAAc;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,QAAQ,SAAS,GAAG,iBAAiB,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,SAAS,GAAG,iBAAiB;AAAA,MACtC;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAElB,eAAO,QAAQ,SAAS,GAAG,iBAAiB,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAOA;AACT;AAaA,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,eAAe;AAAA,EACf,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AACpB;AAmBA,IAAI,qBAAkC,SAAU,cAAc;AAC5D,YAAUG,qBAAoB,YAAY;AAC1C,MAAI,SAAS,aAAaA,mBAAkB;AAO5C,WAASA,oBAAmB,SAAS,cAAc;AACjD,QAAI;AACJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,SAAS,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACnD,oBAAgB,MAAMA,mBAAkB;AACxC,YAAQ,OAAO,KAAK,IAAI;AACxB,oBAAgB,uBAAuB,KAAK,GAAG,UAAU,MAAM;AAC/D,oBAAgB,uBAAuB,KAAK,GAAG,mBAAmB,CAAC;AAMnE,oBAAgB,uBAAuB,KAAK,GAAG,eAA4B,WAAY;AACrF,UAAI,OAAO,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,QAAQ,eAAe;AAC5G,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO,EAAG,SAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,YAC/C,KAAK;AACH,kBAAI,EAAE,MAAM,oBAAoB,gBAAgB;AAC9C,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,qBAAO,SAAS,OAAO,QAAQ;AAAA,YACjC,KAAK;AACH,uBAAS,OAAO;AAChB,qBAAO,OAAO,gBAAgB;AAAA,YAChC,KAAK;AACH,uBAAS,KAAK,SAAS;AACvB,uBAAS,KAAK,MAAM;AACpB,uBAAS,KAAK,SAAS,KAAK,SAAS;AACrC,uBAAS,KAAK;AACd,gCAAkB,SAAS,KAAK,SAAS;AACzC,oBAAM,IAAI,sBAAsB,OAAO,eAAe,CAAC;AACvD,uBAAS,OAAO;AAChB,qBAAO,OAAO,gBAAgB,eAAe;AAAA,YAC/C,KAAK;AACH,oBAAM,kBAAkB;AAAA,YAC1B,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,SAAS,KAAK;AAAA,UACzB;AAAA,QACF,GAAG,OAAO;AAAA,MACZ,CAAC,CAAC;AACF,aAAO,SAAU,IAAI,KAAK;AACxB,eAAO,KAAK,MAAM,MAAM,SAAS;AAAA,MACnC;AAAA,IACF,EAAE,CAAC;AACH,UAAM,SAAS;AACf,UAAM,KAAK,cAAc,SAAS,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,OAAO,CAAC;AAC7F,WAAO;AAAA,EACT;AACA,eAAaA,qBAAoB,CAAC;AAAA,IAChC,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,WAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,IAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAY;AACjB,UAAI,QAAQ,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,SAAS,cAAc,QAAQ,SAAS;AACtH,YAAI,SAAS;AACb,YAAI,eAAe,cAAc;AACjC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO,EAAG,SAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACjD,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,KAAK,oBAAoB,cAAc,MAAM;AAAA,YACtD,KAAK;AACH,kBAAI,EAAE,QAAQ,SAAS,WAAW;AAChC,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,OAAO;AACjB,qBAAO,KAAK,aAAa,cAAc,QAAQ,OAAO;AAAA,YACxD,KAAK;AACH,8BAAgB,UAAU,cAAc,UAAU,WAAY;AAC5D,uBAAO,OAAO,aAAa,cAAc,QAAQ,OAAO;AAAA,cAC1D,CAAC;AACD,6BAAe,KAAK,yBAAyB,cAAc,QAAQ,OAAO;AAC1E,mBAAK,iBAAiB,cAAc,WAAY;AAC9C,6BAAa,OAAO;AACpB,8BAAc,OAAO;AAAA,cACvB,CAAC;AACD,wBAAU,OAAO;AACjB;AAAA,YACF,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,KAAK,mBAAmB,cAAc,MAAM;AAAA,YACrD,KAAK;AACH,oCAAsB,UAAU,QAAQ,CAAC,UAAU,QAAQ,SAAS,YAAY,GAAG,WAAY;AAC7F,uBAAO,OAAO,mBAAmB,cAAc,MAAM;AAAA,cACvD,GAAG,MAAM,KAAK;AACd,mBAAK,iBAAiB,cAAc,WAAY;AAC9C,uBAAO,oBAAoB,OAAO;AAAA,cACpC,CAAC;AAAA,YACH,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,QACF,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,KAAK,KAAK,KAAK,KAAK;AAC3B,eAAO,MAAM,MAAM,MAAM,SAAS;AAAA,MACpC;AACA,aAAO;AAAA,IACT,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAY;AACjB,UAAI,sBAAsB,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,SAAS,cAAc,QAAQ;AAC3H,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO,EAAG,SAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACjD,KAAK;AACH,wBAAU,KAAK;AACf,wBAAU,OAAO;AACjB,qBAAO,OAAO,eAAe;AAAA,YAC/B,KAAK;AACH,wBAAU,KAAK,UAAU;AACzB,wBAAU,OAAO;AACjB,qBAAO,OAAO,UAAU;AAAA,YAC1B,KAAK;AACH,kBAAI,CAAC,UAAU,MAAM;AACnB,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,KAAK;AACf,wBAAU,OAAO;AACjB;AAAA,YACF,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,OAAO,gBAAgB;AAAA,YAChC,KAAK;AACH,wBAAU,KAAK,UAAU;AAAA,YAC3B,KAAK;AACH,wBAAU,KAAK,UAAU;AACzB,wBAAU,KAAK;AAAA,gBACb,UAAU,UAAU;AAAA,gBACpB,UAAU,UAAU;AAAA,cACtB;AACA,wBAAU,GAAG,OAAO,KAAK,UAAU,IAAI,UAAU,EAAE;AAAA,YACrD,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,QACF,GAAG,QAAQ;AAAA,MACb,CAAC,CAAC;AACF,eAAS,mBAAmB,KAAK,KAAK;AACpC,eAAO,oBAAoB,MAAM,MAAM,SAAS;AAAA,MAClD;AACA,aAAO;AAAA,IACT,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAY;AACjB,UAAI,gBAAgB,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,SAAS,cAAc,QAAQ,SAAS;AAC9H,YAAI,qBAAqB,UAAU;AACnC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO,EAAG,SAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACjD,KAAK;AACH,oCAAsB,aAAa,MAAM,GAAG,WAAW,oBAAoB,UAAU,WAAW,oBAAoB;AACpH,kBAAI,OAAO,aAAa,UAAU;AAChC,uBAAO,eAAe,QAAQ;AAAA,cAChC;AACA,kBAAI,EAAE,OAAO,aAAa,WAAW;AACnC,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,kBAAI,EAAE,aAAa,IAAI;AACrB,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,OAAO;AACjB,qBAAO,OAAO,UAAU;AAAA,YAC1B,KAAK;AACH,wBAAU,KAAK,UAAU;AACzB,kBAAI,EAAE,UAAU,OAAO,QAAQ;AAC7B,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,MAAM;AAAA,YACf,KAAK;AACH,wBAAU,OAAO;AACjB;AAAA,YACF,KAAK;AACH,kBAAI,EAAE,WAAW,IAAI;AACnB,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,OAAO;AACjB,qBAAO,OAAO,UAAU;AAAA,YAC1B,KAAK;AACH,wBAAU,KAAK,UAAU;AACzB,kBAAI,EAAE,UAAU,OAAO,OAAO;AAC5B,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,OAAO;AACjB,qBAAO,OAAO,KAAK,EAAE,MAAoB,WAAY;AACnD,oBAAI,QAAQ,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK;AAC5F,yBAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,2BAAO,EAAG,SAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,sBACjD,KAAK;AACH,4BAAI,EAAE,IAAI,SAAS,qBAAqB,QAAQ,gBAAgB;AAC9D,oCAAU,OAAO;AACjB;AAAA,wBACF;AACA,kCAAU,OAAO;AACjB,+BAAO,OAAO,SAAS,IAAI;AAAA,sBAC7B,KAAK;AACH,kCAAU,OAAO;AACjB,+BAAO,OAAO,KAAK,EAAE,MAAM,SAAU,MAAM;AACzC,iCAAO,QAAQ,MAAM,2DAA4D,IAAI;AAAA,wBACvF,CAAC;AAAA,sBACH,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO,UAAU,KAAK;AAAA,oBAC1B;AAAA,kBACF,GAAG,QAAQ;AAAA,gBACb,CAAC,CAAC;AACF,uBAAO,SAAU,MAAM;AACrB,yBAAO,MAAM,MAAM,MAAM,SAAS;AAAA,gBACpC;AAAA,cACF,EAAE,CAAC;AAAA,YACL,KAAK;AACH,mBAAK,aAAa,cAAc,QAAQ,OAAO;AAAA,YACjD,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,OAAO,gBAAgB;AAAA,YAChC,KAAK;AACH,wBAAU,KAAK,UAAU;AACzB,wBAAU,KAAK;AACf,kBAAI,EAAE,UAAU,OAAO,UAAU,KAAK;AACpC,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,gBAAgB,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,QACF,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,aAAa,KAAK,KAAK,MAAM;AACpC,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,cAAc,QAAQ,SAAS;AACtE,UAAI,SAAS;AACb,UAAI,eAAe,QAAQ,cACzB,kBAAkB,QAAQ,iBAC1B,mBAAmB,QAAQ,kBAC3B,oBAAoB,QAAQ,mBAC5B,mBAAmB,QAAQ;AAC7B,UAAI,eAAe,KAAK,IAAI,kBAAkB,KAAK,IAAI,kBAAkB,eAAe,CAAC,IAAI;AAC7F,UAAI,QAAqB,WAAY;AACnC,YAAI,QAAQ,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AACzF,cAAI,MAAM,SAAS,KAAK,KAAK;AAC7B,iBAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,mBAAO,EAAG,SAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACjD,KAAK;AACH,0BAAU,KAAK,aAAa,MAAM,EAAE,aAAa;AACjD,oBAAI,UAAU,IAAI;AAChB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,OAAO,UAAU;AAAA,cAC1B,KAAK;AACH,0BAAU,KAAK,UAAU;AACzB,0BAAU,KAAK,UAAU,OAAO;AAAA,cAClC,KAAK;AACH,oBAAI,CAAC,UAAU,IAAI;AACjB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,KAAK,aAAa,MAAM,EAAE;AACpC,0BAAU,OAAO;AACjB,uBAAO,OAAO,eAAe;AAAA,cAC/B,KAAK;AACH,0BAAU,KAAK,UAAU;AACzB,uBAAO,UAAU,KAAK,UAAU;AAChC,0BAAU,KAAK,IAAI,IAAI;AACvB,uBAAO,IAAI,UAAU,OAAO,IAAI,CAAC;AACjC,oBAAI,EAAE,UAAU,kBAAkB;AAChC,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,OAAO,YAAY,QAAQ,CAAC;AAAA,cACrC,KAAK;AACH,uBAAO,eAAe,aAAa,MAAM,EAAE,QAAQ;AACnD,uBAAO,IAAI,uBAAuB;AAClC,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,oBAAI,EAAE,UAAU,eAAe;AAC7B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,sBAAM,UAAU;AAChB,sBAAM;AACN,6BAAa,MAAM,OAAO,MAAM,OAAO,IAAI;AAC3C,0BAAU,OAAO;AACjB,uBAAO,OAAO,YAAY,QAAQ,aAAa,KAAK,KAAK,IAAI,CAAC;AAAA,cAChE,KAAK;AACH,uBAAO,IAAI,wBAAwB;AAAA,cACrC,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,UACF,GAAG,QAAQ;AAAA,QACb,CAAC,CAAC;AACF,eAAO,SAASC,SAAQ;AACtB,iBAAO,MAAM,MAAM,MAAM,SAAS;AAAA,QACpC;AAAA,MACF,EAAE;AACF,UAAI,WAAW,YAAY,WAAY;AACrC,eAAO,MAAM;AAAA,MACf,GAAG,YAAY;AACf,aAAO;AAAA,QACL,QAAQ,SAAS,SAAS;AACxB,iBAAO,cAAc,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,EAKF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,KAAK;AACvB,UAAI;AACJ,OAAC,eAAe,KAAK,YAAY,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,MAAM,uBAAuB,OAAO,GAAG,CAAC;AAAA,IACxI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,oBAAoB,cAAc,OAAO;AAChD,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,cAAI,QAAQ,SAASA,SAAQ;AAC3B,gBAAI,aAAa,eAAe,OAAO;AACrC,sBAAQ;AAAA,YACV,OAAO;AACL,2BAAa,iBAAiB,oBAAoBA,QAAO;AAAA,gBACvD,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AACA,gBAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA;AAAA,EACF,CAAC,CAAC;AACF,SAAOD;AACT,EAAgB,iBAAiB,WAAW,CAAC;AAE7C,IAAI,YAAY,oBAAI,QAAQ;AAC5B,IAAI,WAAW,oBAAI,QAAQ;AAC3B,IAAI,aAAa,CAAC;AAClB,IAAI,SAAsB,WAAY;AASpC,WAASE,QAAO,SAAS;AACvB,QAAI,QAAQ;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,oBAAgB,MAAMA,OAAM;AAE5B,QAAI,OAAO,UAAU,mBAAmB,QAAQ;AAC9C,UAAI,QAAQ,SAAS,KAAK,OAAO,WAAW,QAAQ,MAAM;AACxD,gBAAQ,KAAK,6EAA6E;AAAA,MAC5F;AACA,gBAAU,QAAQ,CAAC;AAAA,IACrB;AAGA,QAAI,OAAO,aAAa,eAAe,OAAO,YAAY,UAAU;AAClE,gBAAU,SAAS,eAAe,OAAO;AAAA,IAC3C;AAGA,QAAI,CAAC,aAAa,OAAO,GAAG;AAC1B,YAAM,IAAI,UAAU,qDAAqD;AAAA,IAC3E;AAGA,QAAI,QAAQ,aAAa,UAAU;AACjC,UAAI,SAAS,QAAQ,cAAc,QAAQ;AAC3C,UAAI,QAAQ;AACV,kBAAU;AAAA,MACZ;AAAA,IACF;AAGA,QAAI,QAAQ,aAAa,YAAY,CAAC,WAAW,QAAQ,aAAa,KAAK,KAAK,EAAE,GAAG;AACnF,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AAGA,QAAI,UAAU,IAAI,OAAO,GAAG;AAC1B,aAAO,UAAU,IAAI,OAAO;AAAA,IAC9B;AACA,SAAK,UAAU,QAAQ,cAAc;AACrC,SAAK,UAAU;AACf,SAAK,SAAS;AACd,QAAI,eAAe,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACxD,YAAM,aAAa,SAAU,OAAO;AAClC,YAAI,CAAC,WAAW,MAAM,MAAM,KAAK,MAAM,QAAQ,kBAAkB,MAAM,QAAQ;AAC7E;AAAA,QACF;AACA,YAAI,MAAM,WAAW,KAAK;AACxB,gBAAM,SAAS,MAAM;AAAA,QACvB;AACA,YAAI,OAAO,iBAAiB,MAAM,IAAI;AACtC,YAAI,UAAU,QAAQ,KAAK,UAAU;AACrC,YAAI,eAAe,WAAW,KAAK,QAAQ,KAAK,KAAK,WAAW;AAChE,YAAI,cAAc;AAChB,cAAI,QAAQ,IAAI,MAAM,KAAK,KAAK,OAAO;AACvC,gBAAM,OAAO,KAAK,KAAK;AACvB,iBAAO,KAAK;AACZ;AAAA,QACF;AACA,YAAI,eAAe,QAAQ,KAAK,UAAU;AAC1C,YAAI,iBAAiB,QAAQ,KAAK,WAAW;AAC7C,YAAI,gBAAgB,gBAAgB;AAClC,gBAAM,QAAQ,aAAa,cAAc,MAAM;AAC/C,kBAAQ;AACR;AAAA,QACF;AACA,oBAAY,OAAO,IAAI;AAAA,MACzB;AACA,YAAM,QAAQ,iBAAiB,WAAW,MAAM,UAAU;AAC1D,UAAI,MAAM,QAAQ,aAAa,UAAU;AACvC,YAAI,SAAS,oBAAoB,SAAS,OAAO;AACjD,YAAI,MAAM,YAAY,MAAM;AAC5B,sBAAc,KAAK,QAAQ,OAAO,EAAE,KAAK,SAAU,MAAM;AACvD,cAAIC,UAAS,YAAY,MAAM,OAAO;AAGtC,gBAAM,UAAUA;AAChB,gBAAM,mBAAmB;AACzB,wBAAc,SAASA,OAAM;AAC7B,oBAAU,IAAI,MAAM,SAAS,KAAK;AAClC,iBAAO;AAAA,QACT,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB;AAAA,IACF,CAAC;AAGD,aAAS,IAAI,MAAM,YAAY;AAC/B,cAAU,IAAI,KAAK,SAAS,IAAI;AAIhC,QAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,kBAAY,MAAM,MAAM;AAAA,IAC1B;AACA,QAAI,WAAW,WAAW;AACxB,UAAI,iBAAiB,SAASC,kBAAiB;AAC7C,eAAO,WAAW,KAAK;AAAA,MACzB;AACA,WAAK,0BAA0B,WAAY;AACzC,YAAI,WAAW,cAAc;AAC3B,wBAAc,OAAO,wBAAwB,cAAc;AAAA,QAC7D,OAAO;AACL,yBAAe,OAAO,wBAAwB,cAAc;AAAA,QAC9D;AAEA,cAAM,MAAM,EAAE,KAAK,WAAY;AAC7B,sBAAY,OAAO,oBAAoB,WAAW,YAAY;AAAA,QAChE,CAAC;AAAA,MACH;AACA,iBAAW,GAAG,oBAAoB,KAAK,uBAAuB;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AASA,eAAaF,SAAQ,CAAC;AAAA,IACpB,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,MAAM;AAC/B,UAAI,SAAS;AACb,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,aAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,MACjC;AACA,UAAI,SAAS,UAAa,SAAS,MAAM;AACvC,cAAM,IAAI,UAAU,8BAA8B;AAAA,MACpD;AACA,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAI5C,eAAO,OAAO,MAAM,EAAE,KAAK,WAAY;AACrC,wBAAc,QAAQ,MAAM;AAAA,YAC1B;AAAA,YACA;AAAA,UACF,CAAC;AACD,sBAAY,QAAQ,MAAM,IAAI;AAAA,QAChC,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,MAAM;AACxB,UAAI,SAAS;AACb,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,eAAO,cAAc,MAAM,KAAK;AAKhC,eAAO,OAAO,MAAM,EAAE,KAAK,WAAY;AACrC,wBAAc,QAAQ,MAAM;AAAA,YAC1B;AAAA,YACA;AAAA,UACF,CAAC;AACD,sBAAY,QAAQ,IAAI;AAAA,QAC1B,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,MAAM,OAAO;AAC/B,UAAI,SAAS;AACb,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,eAAO,cAAc,MAAM,KAAK;AAChC,YAAI,UAAU,UAAa,UAAU,MAAM;AACzC,gBAAM,IAAI,UAAU,+BAA+B;AAAA,QACrD;AAKA,eAAO,OAAO,MAAM,EAAE,KAAK,WAAY;AACrC,wBAAc,QAAQ,MAAM;AAAA,YAC1B;AAAA,YACA;AAAA,UACF,CAAC;AACD,sBAAY,QAAQ,MAAM,KAAK;AAAA,QACjC,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG,WAAW,UAAU;AACtC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,UAAU,8BAA8B;AAAA,MACpD;AACA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,UAAU,oCAAoC;AAAA,MAC1D;AACA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,kCAAkC;AAAA,MACxD;AACA,UAAI,YAAY,aAAa,MAAM,SAAS,OAAO,SAAS,CAAC;AAC7D,UAAI,UAAU,WAAW,GAAG;AAC1B,aAAK,WAAW,oBAAoB,SAAS,EAAE,MAAM,WAAY;AAAA,QAGjE,CAAC;AAAA,MACH;AACA,oBAAc,MAAM,SAAS,OAAO,SAAS,GAAG,QAAQ;AAAA,IAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,WAAW,UAAU;AACvC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,UAAU,8BAA8B;AAAA,MACpD;AACA,UAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,cAAM,IAAI,UAAU,kCAAkC;AAAA,MACxD;AACA,UAAI,eAAe,eAAe,MAAM,SAAS,OAAO,SAAS,GAAG,QAAQ;AAG5E,UAAI,cAAc;AAChB,aAAK,WAAW,uBAAuB,SAAS,EAAE,MAAM,SAAU,GAAG;AAAA,QAGrE,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,SAAS;AACjC,aAAO,KAAK,WAAW,aAAa,OAAO;AAAA,IAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,eAAe,SAAS,IAAI,IAAI,KAAK,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC9E,eAAO,IAAI,MAAM,oCAAoC,CAAC;AAAA,MACxD,CAAC;AACD,aAAO,QAAQ,QAAQ,YAAY;AAAA,IACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,MAAM;AAChC,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAChF,aAAO,KAAK,WAAW,eAAe;AAAA,QACpC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,IAAI;AACjC,aAAO,KAAK,WAAW,kBAAkB,EAAE;AAAA,IAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,UAAU,MAAM;AAC9C,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACjD;AACA,aAAO,KAAK,WAAW,mBAAmB;AAAA,QACxC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB;AACjC,aAAO,KAAK,WAAW,kBAAkB;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,aAAO,KAAK,WAAW,OAAO;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,aAAO,KAAK,WAAW,MAAM;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,WAAW,WAAW;AACxB,eAAO,WAAW,QAAQ,KAAK,OAAO;AAAA,MACxC;AACA,aAAO,KAAK,WAAW,mBAAmB;AAAA,IAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,WAAW,WAAW;AACxB,eAAO,WAAW,KAAK;AAAA,MACzB;AACA,aAAO,KAAK,WAAW,gBAAgB;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,UAAI,WAAW,WAAW;AACxB,eAAO,QAAQ,QAAQ,WAAW,YAAY;AAAA,MAChD;AACA,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,0BAA0B;AACxC,aAAO,KAAK,WAAW,yBAAyB;AAAA,IAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,aAAO,KAAK,WAAW,sBAAsB;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,sBAAsB;AACpC,aAAO,KAAK,IAAI,kBAAkB;AAAA,IACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,aAAO,KAAK,WAAW,sBAAsB;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAO,KAAK,WAAW,QAAQ;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,SAAS;AACb,aAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,iBAAS,OAAO,MAAM;AACtB,kBAAU,OAAO,OAAO,OAAO;AAC/B,YAAI,OAAO,kBAAkB;AAC3B,oBAAU,OAAO,OAAO,gBAAgB;AACxC,iBAAO,iBAAiB,gBAAgB,wBAAwB;AAAA,QAClE;AACA,YAAI,OAAO,WAAW,OAAO,QAAQ,aAAa,YAAY,OAAO,QAAQ,YAAY;AAGvF,cAAI,OAAO,QAAQ,WAAW,cAAc,OAAO,oBAAoB,OAAO,qBAAqB,OAAO,QAAQ,YAAY;AAC5H,mBAAO,QAAQ,WAAW,WAAW,YAAY,OAAO,QAAQ,UAAU;AAAA,UAC5E,OAAO;AACL,mBAAO,QAAQ,WAAW,YAAY,OAAO,OAAO;AAAA,UACtD;AAAA,QACF;AAIA,YAAI,OAAO,WAAW,OAAO,QAAQ,aAAa,SAAS,OAAO,QAAQ,YAAY;AACpF,iBAAO,QAAQ,gBAAgB,wBAAwB;AACvD,cAAI,SAAS,OAAO,QAAQ,cAAc,QAAQ;AAClD,cAAI,UAAU,OAAO,YAAY;AAG/B,gBAAI,OAAO,WAAW,cAAc,OAAO,oBAAoB,OAAO,qBAAqB,OAAO,YAAY;AAC5G,qBAAO,WAAW,WAAW,YAAY,OAAO,UAAU;AAAA,YAC5D,OAAO;AACL,qBAAO,WAAW,YAAY,MAAM;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AACA,eAAO,QAAQ,oBAAoB,WAAW,OAAO,UAAU;AAC/D,YAAI,WAAW,WAAW;AACxB,qBAAW,IAAI,oBAAoB,OAAO,uBAAuB;AAAA,QACnE;AACA,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,KAAK,IAAI,WAAW;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,WAAW;AACtC,aAAO,KAAK,IAAI,aAAa,SAAS;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,aAAO,KAAK,IAAI,aAAa;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,QAAQ;AACrC,aAAO,KAAK,IAAI,eAAe,MAAM;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,aAAO,KAAK,IAAI,gBAAgB;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK,IAAI,OAAO;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO,QAAQ,IAAI,CAAC,KAAK,IAAI,UAAU,GAAG,KAAK,IAAI,UAAU,GAAG,KAAK,IAAI,YAAY,GAAG,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAChH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC9B,aAAO,KAAK,IAAI,SAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,QAAQ;AAChC,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAO,OAAO,IAAI,UAAU,4BAA4B,CAAC;AAAA,QAC3D,CAAC;AAAA,MACH;AACA,UAAI,cAAc,IAAI,QAAQ,SAAU,SAAS;AAC/C,eAAO,QAAQ,IAAI;AAAA,MACrB,CAAC;AACD,UAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,YAAY,OAAO,CAAC,CAAC,IAAI,aAAa,OAAO,CAAC,IAAI,KAAK,IAAI,YAAY,OAAO,CAAC,CAAC,IAAI,aAAa,OAAO,CAAC,IAAI,KAAK,IAAI,cAAc,OAAO,CAAC,CAAC,IAAI,aAAa,OAAO,CAAC,IAAI,KAAK,IAAI,aAAa,OAAO,CAAC,CAAC,IAAI,WAAW;AAClQ,aAAO,QAAQ,IAAI,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,KAAK,IAAI,WAAW;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,aAAO,KAAK,IAAI,aAAa;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,aAAa;AAC1C,aAAO,KAAK,IAAI,eAAe,WAAW;AAAA,IAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK,IAAI,OAAO;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,aAAO,KAAK,IAAI,MAAM;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ,MAAM;AAC5B,aAAO,KAAK,IAAI,QAAQ,IAAI;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC9B,aAAO,KAAK,IAAI,SAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK,IAAI,OAAO;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,aAAO,KAAK,IAAI,cAAc;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,cAAc;AAC5C,aAAO,KAAK,IAAI,gBAAgB,YAAY;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,KAAK,IAAI,WAAW;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,KAAK,IAAI,SAAS;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,SAAS;AAClC,aAAO,KAAK,IAAI,WAAW,OAAO;AAAA,IACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gCAAgC;AAC9C,aAAO,KAAK,IAAI,4BAA4B;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB;AACvC,aAAO,KAAK,IAAI,qBAAqB;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,KAAK,IAAI,SAAS;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,aAAO,KAAK,IAAI,gBAAgB;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,KAAK,IAAI,SAAS;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,aAAO,KAAK,IAAI,aAAa;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,aAAO,KAAK,IAAI,UAAU;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,QAAQ;AAChC,aAAO,KAAK,IAAI,UAAU,MAAM;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAY;AACjB,UAAI,gBAAgB,kBAAgC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,cAAc,SAAS;AACrH,YAAI,SAAS;AACb,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO,EAAG,SAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,YAC/C,KAAK;AACH,kBAAI,cAAc;AAChB,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,oBAAM,IAAI,UAAU,mCAAmC;AAAA,YACzD,KAAK;AACH,uBAAS,OAAO;AAChB,qBAAO,KAAK,MAAM;AAAA,YACpB,KAAK;AACH,0BAAY,IAAI,mBAAmB,MAAM,cAAc,OAAO;AAC9D,0BAAY,MAAM,2BAA2B;AAC7C,wBAAU,iBAAiB,cAAc,WAAY;AACnD,uBAAO,YAAY,QAAQ,8BAA8B;AAAA,cAC3D,CAAC;AACD,qBAAO,SAAS,OAAO,UAAU,SAAS;AAAA,YAC5C,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,SAAS,KAAK;AAAA,UACzB;AAAA,QACF,GAAG,SAAS,IAAI;AAAA,MAClB,CAAC,CAAC;AACF,eAAS,aAAa,IAAI,KAAK;AAC7B,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,CAAC,CAAC;AACF,SAAOA;AACT,EAAE;AACF,IAAI,CAAC,QAAQ;AACX,eAAa,qBAAqB;AAClC,mBAAiB;AACjB,eAAa;AACb,0BAAwB;AACxB,oBAAkB;AACpB;AAEA,IAAO,oBAAQ;;;AC19Gf,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,gBAAgB,OAAO,QAAQ,CAAC,GAAG;AAC1C,QAAM,cAAc;AAAA,IAClB,KAAK,mBAAmB,OAAO,KAAK;AAAA,IACpC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ;AAChB,gBAAY,aAAa,IAAI,KAAK,UAAU,MAAM,MAAM;AAAA,EAC1D;AACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAiBS,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAG7C;AACA,SAAS,mBAAmB,OAAO,OAAO;AACxC,MAAI,CAAC,MAAM,IAAK;AAChB,QAAM,UAAU,MAAM,IAAI,MAAM,SAAS;AACzC,QAAM,QAAQ,WAAW,QAAQ,CAAC;AAClC,QAAM,SAAS,WAAW,QAAQ,CAAC;AACnC,QAAM,SAAS;AAAA;AAAA,IAEb,UAAU,MAAM,aAAa,KAAK,OAAO;AAAA,IACzC,UAAU,MAAM;AAAA,IAChB,MAAM,MAAM;AAAA,IACZ,OAAO,MAAM;AAAA,IACb,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM,WAAW;AAAA,IAC1B,aAAa;AAAA,IACb,WAAW,MAAM;AAAA,IACjB,GAAG;AAAA;AAAA,IAEH,GAAG,MAAM;AAAA,EACX;AACA,SAAO,GAAG,UAAU,IAAI,KAAK,IAAI,UAAU,MAAM,CAAC;AACpD;AACA,IAAM,oBAAN,eAAiC,WAAW,eAAe,MAAM;AACjE,GAAG;AAAA,EACD,OAAO,kBAAkB;AAAA,EACzB,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,EAC1C,OAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe,IAAI,cAAc;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU,CAAC,KAAK;AAAA,EAChB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,cAAc;AAAA,EACd,eAAe;AAAA,EACf,UAAU;AAAA,EACV,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,sBAAsB,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,EACrG;AAAA,EACA,iBAAiB;AACf,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,mBAAmB,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,EAClG;AAAA,EACA,0BAA0B;AACxB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,4BAA4B,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,EAC3G;AAAA,EACA,uBAAuB;AACrB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,yBAAyB,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,EACxG;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,OAAO;AACX,QAAI;AACJ,QAAI,KAAK,eAAgB;AACzB,UAAM,cAAc,CAAC,KAAK;AAC1B,QAAI,KAAK,WAAY,MAAK,eAAe,IAAI,cAAc;AAC3D,SAAK,aAAa;AAClB,WAAO,KAAK,iBAAiB,QAAQ,QAAQ;AAC7C,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,UAAU,CAAC,KAAK;AACrB,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC,QAAI,SAAS,KAAK;AAClB,SAAK,MAAM;AACX,QAAI,CAAC,KAAK,KAAK;AACb;AAAA,IACF;AACA,SAAK,cAAc,IAAI,MAAM,WAAW,CAAC;AACzC,UAAM,UAAU;AAAA,MACd,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,SAAS,KAAK,WAAW;AAAA,MACzB,aAAa;AAAA,MACb,WAAW,KAAK,aAAa,WAAW;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AACA,UAAM,WAAW,YAAY;AAC3B,WAAK,cAAc;AACnB,WAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,UAAI,KAAK,KAAK;AACZ,aAAK,SAAS,MAAM,KAAK,IAAI,SAAS;AACtC,aAAK,UAAU,MAAM,KAAK,IAAI,UAAU;AACxC,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C,aAAK,YAAY,MAAM,KAAK,IAAI,YAAY;AAC5C,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAAA,MAChD;AACA,WAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C,WAAK,aAAa,QAAQ;AAAA,IAC5B;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,MAAM;AACX,YAAM,KAAK,IAAI,UAAU;AAAA,QACvB,GAAG;AAAA,QACH,KAAK,KAAK;AAAA,MACZ,CAAC;AACD,YAAM,SAAS;AACf,YAAM,KAAK;AACX;AAAA,IACF;AACA,SAAK,UAAU;AACf,QAAI,UAAU,KAAK,KAAK,eAAe,OAAO,SAAS,GAAG,cAAc,QAAQ;AAChF,QAAI,eAAe,QAAQ;AACzB,WAAK,UAAU,KAAK,MAAM,OAAO,aAAa,aAAa,KAAK,IAAI;AAAA,IACtE;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAClC,WAAK,WAAW,YAAY,gBAAgB,qBAAqB,KAAK,UAAU,GAAG,IAAI;AACvF,eAAS,KAAK,WAAW,cAAc,QAAQ;AAAA,IACjD;AACA,SAAK,MAAM,IAAI,kBAAe,MAAM;AACpC,UAAM,aAAa,MAAM;AACvB,WAAK,IAAI,IAAI,UAAU,UAAU;AACjC,eAAS;AAAA,IACX;AACA,SAAK,IAAI,GAAG,UAAU,UAAU;AAChC,SAAK,IAAI,GAAG,eAAe,MAAM;AAC/B,UAAI,KAAK,SAAS;AAChB,aAAK,UAAU;AACf,aAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AAAA,MACtC;AACA,WAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,SAAK,IAAI,GAAG,QAAQ,MAAM;AACxB,UAAI,CAAC,KAAK,QAAS;AACnB,WAAK,UAAU;AACf,WAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AAAA,IACtC,CAAC;AACD,SAAK,IAAI,GAAG,WAAW,MAAM;AAC3B,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,SAAK,IAAI,GAAG,WAAW,MAAM;AAC3B,WAAK,WAAW;AAChB,WAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,SAAK,IAAI,GAAG,UAAU,MAAM;AAC1B,WAAK,WAAW;AAChB,WAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,SAAK,IAAI,GAAG,SAAS,MAAM;AACzB,WAAK,UAAU;AACf,WAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,SAAK,IAAI,GAAG,SAAS,MAAM;AACzB,WAAK,UAAU;AACf,WAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,SAAK,IAAI,GAAG,cAAc,CAAC,EAAE,aAAa,MAAM;AAC9C,WAAK,gBAAgB;AACrB,WAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,IAC5C,CAAC;AACD,SAAK,IAAI,GAAG,gBAAgB,OAAO,EAAE,OAAO,MAAM;AAChD,WAAK,UAAU;AACf,UAAI,KAAK,KAAK;AACZ,aAAK,SAAS,MAAM,KAAK,IAAI,SAAS;AAAA,MACxC;AACA,WAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,SAAK,IAAI,GAAG,kBAAkB,CAAC,EAAE,SAAS,MAAM;AAC9C,WAAK,YAAY;AACjB,WAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAAA,IAChD,CAAC;AACD,SAAK,IAAI,GAAG,cAAc,CAAC,EAAE,QAAQ,MAAM;AACzC,WAAK,eAAe;AACpB,WAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,IAC5C,CAAC;AACD,SAAK,IAAI,GAAG,YAAY,CAAC,EAAE,QAAQ,MAAM;AACvC,WAAK,YAAY;AACjB,WAAK,cAAc,IAAI,MAAM,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,SAAK,IAAI,GAAG,UAAU,CAAC,EAAE,YAAY,YAAY,MAAM;AACrD,WAAK,cAAc;AACnB,WAAK,eAAe;AACpB,WAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,UAAM,KAAK;AAAA,EACb;AAAA,EACA,MAAM,yBAAyB,UAAU,UAAU,UAAU;AAC3D,QAAI,aAAa,SAAU;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACV,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AACA,UAAM,KAAK;AACX,YAAQ,UAAU;AAAA,MAChB,KAAK,QAAQ;AACX,aAAK,IAAI,QAAQ,KAAK,IAAI;AAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,QAAI;AACJ,SAAK,UAAU;AACf,SAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AACpC,UAAM,KAAK;AACX,QAAI;AACF,cAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,KAAK;AAAA,IACpD,SAAS,OAAO;AACd,WAAK,UAAU;AACf,WAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AACrC,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI;AACJ,UAAM,KAAK;AACX,YAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,MAAM;AAAA,EACrD;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,gBAAgB,KAAK;AAAA,EACnC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,IAAI,KAAK;AACX,QAAI,KAAK,OAAO,IAAK;AACrB,SAAK,aAAa,OAAO,GAAG;AAAA,EAC9B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,WAAW;AACb,QAAI,KAAK,YAAY,GAAG;AACtB,aAAO,iBAAiB,GAAG,KAAK,SAAS;AAAA,IAC3C;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK,eAAe,IAAK;AAC7B,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,eAAe,GAAG,EAAE,MAAM,MAAM;AAAA,MACtE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,QAAI,KAAK,gBAAgB,IAAK;AAC9B,SAAK,gBAAgB,SAAS,QAAQ,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa,MAAM;AAAA,EACjC;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,QAAI,KAAK,QAAQ,IAAK;AACtB,SAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,QAAI,KAAK,SAAS,IAAK;AACvB,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,SAAS,GAAG,EAAE,MAAM,MAAM;AAAA,MAChE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,QAAI,KAAK,gBAAgB,IAAK;AAC9B,SAAK,gBAAgB;AACrB,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,gBAAgB,GAAG,EAAE,MAAM,MAAM;AAAA,MACvE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK,eAAe,IAAK;AAC7B,SAAK,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AAAA,EAClD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,aAAa,QAAQ;AAAA,EACnC;AAAA,EACA,IAAI,OAAO,KAAK;AACd,QAAI,KAAK,UAAU,IAAK;AACxB,SAAK,aAAa,UAAU,GAAG,GAAG,EAAE;AAAA,EACtC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,KAAK;AACd,QAAI,KAAK,UAAU,IAAK;AACxB,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,UAAU,GAAG,EAAE,MAAM,MAAM;AAAA,MACjE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAM,QAAQ,KAAK,IAAI;AACvB,aAAO,KAAK,IAAI;AAChB,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,WAAW,GAAG,CAAC;AAAA,QACxC,SAAQ,IAAI,WAAW,GAAG,CAAC,KAAK,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ;AACtJ;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,IAAI,gBAAgB,aAAa,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,QAAQ,QAAQ,QAAQ,GAAI,GAAE,GAAG,IAAI;AAAA,aAChC,QAAQ,MAAO,GAAE,GAAG,IAAI;AAAA,aACxB,OAAO,KAAM,GAAE,GAAG,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,cAAc;AAC7B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAC7B,GAAG;AACD,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,iBAAiB,OAAO,KAAK;AACpC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,oBAAoB,KAAK;AAAA,EAClC,WAAW,SAAS,QAAQ,OAAO,QAAQ,UAAU,KAAK,QAAQ,GAAG;AACnE,WAAO,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,EACrC;AACA,SAAO,oBAAoB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;AAC3C;AACA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,iBAAiB,QAAQ;AAAA,IAC9B,OAAO;AAAA,MACL,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACH,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,aAAa,GAAG;AAC9E,aAAW,eAAe,OAAO,eAAe,iBAAiB;AACnE;AACA,IAAI,8BAA8B;;;AFtelC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAG;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["self", "value", "next", "key", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "_construct", "Parent", "args", "Class", "_wrapNativeSuper", "oEmbedParameters", "subscribe", "WeakMap", "timer", "builtInProp", "idx", "Promise", "resolve", "reject", "callback", "handleError", "onMessage", "_loop", "screenfull", "onFullScreenEntered", "onFullScreenExit", "TimingSrcConnector", "check", "Player", "iframe", "exitFullscreen", "React"]}