{"name": "hls-video-element", "version": "1.5.6", "description": "Custom element (web component) for playing video using the HTTP Live Streaming (HLS) format. Uses HLS.js.", "author": "@muxinc", "license": "MIT", "homepage": "https://github.com/muxinc/media-elements#readme", "bugs": {"url": "https://github.com/muxinc/media-elements/issues"}, "repository": {"type": "git", "url": "git+https://github.com/muxinc/media-elements.git", "directory": "packages/hls-video-element"}, "files": ["hls-video-element.d.ts", "dist"], "type": "module", "types": "./dist/hls-video-element.d.ts", "main": "./dist/hls-video-element.js", "exports": {".": {"types": "./dist/hls-video-element.d.ts", "import": "./dist/hls-video-element.js", "require": "./dist/cjs/hls-video-element.js", "default": "./dist/hls-video-element.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js", "require": "./dist/cjs/react.js", "default": "./dist/react.js"}}, "typesVersions": {"*": {"react": ["./dist/react.d.ts"], "*": ["./dist/hls-video-element.d.ts"]}}, "scripts": {"lint": "eslint *.js", "test": "wet test --coverage", "serve": "wet serve --cors --liver<PERSON><PERSON>", "build:react": "build-react-wrapper", "build": "run-s build:*"}, "dependencies": {"custom-media-element": "^1.4.5", "hls.js": "^1.6.5", "media-tracks": "^0.3.3"}, "devDependencies": {"build-react-wrapper": "^0.2.2", "npm-run-all": "^4.1.5", "wet-run": "^1.2.5"}, "keywords": ["HLS", "video", "player"]}