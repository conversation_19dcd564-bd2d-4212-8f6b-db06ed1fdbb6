{"version": 3, "sources": ["../../react-player/dist/players.js", "../../react-player/dist/patterns.js", "../../react-player/dist/HtmlPlayer.js", "../../react-player/dist/ReactPlayer.js", "../../react-player/dist/props.js", "../../react-player/dist/Player.js", "../../react-player/dist/index.js"], "sourcesContent": ["import { lazy } from \"react\";\nimport { canPlay } from \"./patterns.js\";\nimport HtmlPlayer from \"./HtmlPlayer.js\";\nconst Players = [\n  {\n    key: \"hls\",\n    name: \"hls.js\",\n    canPlay: canPlay.hls,\n    canEnablePIP: () => true,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerHls' */\n        \"hls-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"dash\",\n    name: \"dash.js\",\n    canPlay: canPlay.dash,\n    canEnablePIP: () => true,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerDash' */\n        \"dash-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"mux\",\n    name: \"Mux\",\n    canPlay: canPlay.mux,\n    canEnablePIP: () => true,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerMux' */\n        \"@mux/mux-player-react\"\n      )\n    )\n  },\n  {\n    key: \"youtube\",\n    name: \"YouTube\",\n    canPlay: canPlay.youtube,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerYouTube' */\n        \"youtube-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"vimeo\",\n    name: \"Vimeo\",\n    canPlay: canPlay.vimeo,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerVimeo' */\n        \"vimeo-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"wistia\",\n    name: \"Wistia\",\n    canPlay: canPlay.wistia,\n    canEnablePIP: () => true,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerWistia' */\n        \"wistia-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"spotify\",\n    name: \"Spotify\",\n    canPlay: canPlay.spotify,\n    canEnablePIP: () => false,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerSpotify' */\n        \"spotify-audio-element/react\"\n      )\n    )\n  },\n  {\n    key: \"twitch\",\n    name: \"Twitch\",\n    canPlay: canPlay.twitch,\n    canEnablePIP: () => false,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerTwitch' */\n        \"twitch-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"tiktok\",\n    name: \"TikTok\",\n    canPlay: canPlay.tiktok,\n    canEnablePIP: () => false,\n    player: lazy(\n      () => import(\n        /* webpackChunkName: 'reactPlayerTiktok' */\n        \"tiktok-video-element/react\"\n      )\n    )\n  },\n  {\n    key: \"html\",\n    name: \"html\",\n    canPlay: canPlay.html,\n    canEnablePIP: () => true,\n    player: HtmlPlayer\n  }\n];\nvar players_default = Players;\nexport {\n  players_default as default\n};\n", "const AUDIO_EXTENSIONS = /\\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\\?)/i;\nconst VIDEO_EXTENSIONS = /\\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\\d+]+)?($|\\?)/i;\nconst HLS_EXTENSIONS = /\\.(m3u8)($|\\?)/i;\nconst DASH_EXTENSIONS = /\\.(mpd)($|\\?)/i;\nconst MATCH_URL_MUX = /stream\\.mux\\.com\\/(?!\\w+\\.m3u8)(\\w+)/;\nconst MATCH_URL_YOUTUBE = /(?:youtu\\.be\\/|youtube(?:-nocookie|education)?\\.com\\/(?:embed\\/|v\\/|watch\\/|watch\\?v=|watch\\?.+&v=|shorts\\/|live\\/))((\\w|-){11})|youtube\\.com\\/playlist\\?list=|youtube\\.com\\/user\\//;\nconst MATCH_URL_VIMEO = /vimeo\\.com\\/(?!progressive_redirect).+/;\nconst MATCH_URL_WISTIA = /(?:wistia\\.(?:com|net)|wi\\.st)\\/(?:medias|embed)\\/(?:iframe\\/)?([^?]+)/;\nconst MATCH_URL_SPOTIFY = /open\\.spotify\\.com\\/(\\w+)\\/(\\w+)/i;\nconst MATCH_URL_TWITCH = /(?:www\\.|go\\.)?twitch\\.tv\\/([a-zA-Z0-9_]+|(videos?\\/|\\?video=)\\d+)($|\\?)/;\nconst MATCH_URL_TIKTOK = /tiktok\\.com\\/(?:@[^/]+\\/video\\/)?(\\d+)(?:\\/([\\w-]+))?/;\nconst canPlayFile = (url, test) => {\n  if (Array.isArray(url)) {\n    for (const item of url) {\n      if (typeof item === \"string\" && canPlayFile(item, test)) {\n        return true;\n      }\n      if (canPlayFile(item.src, test)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  return test(url);\n};\nconst canPlay = {\n  html: (url) => canPlayFile(url, (u) => AUDIO_EXTENSIONS.test(u) || VIDEO_EXTENSIONS.test(u)),\n  hls: (url) => canPlayFile(url, (u) => HLS_EXTENSIONS.test(u)),\n  dash: (url) => canPlayFile(url, (u) => DASH_EXTENSIONS.test(u)),\n  mux: (url) => MATCH_URL_MUX.test(url),\n  youtube: (url) => MATCH_URL_YOUTUBE.test(url),\n  vimeo: (url) => MATCH_URL_VIMEO.test(url) && !VIDEO_EXTENSIONS.test(url) && !HLS_EXTENSIONS.test(url),\n  wistia: (url) => MATCH_URL_WISTIA.test(url),\n  spotify: (url) => MATCH_URL_SPOTIFY.test(url),\n  twitch: (url) => MATCH_URL_TWITCH.test(url),\n  tiktok: (url) => MATCH_URL_TIKTOK.test(url)\n};\nexport {\n  AUDIO_EXTENSIONS,\n  DASH_EXTENSIONS,\n  HLS_EXTENSIONS,\n  MATCH_URL_MUX,\n  MATCH_URL_SPOTIFY,\n  MATCH_URL_TIKTOK,\n  MATCH_URL_TWITCH,\n  MATCH_URL_VIMEO,\n  MATCH_URL_WISTIA,\n  MATCH_URL_YOUTUBE,\n  VIDEO_EXTENSIONS,\n  canPlay\n};\n", "import React from \"react\";\nimport { AUDIO_EXTENSIONS } from \"./patterns.js\";\nconst HtmlPlayer = React.forwardRef((props, ref) => {\n  const Media = AUDIO_EXTENSIONS.test(`${props.src}`) ? \"audio\" : \"video\";\n  return /* @__PURE__ */ React.createElement(Media, { ...props, ref }, props.children);\n});\nvar HtmlPlayer_default = HtmlPlayer;\nexport {\n  HtmlPlayer_default as default\n};\n", "import React, { lazy, Suspense, useEffect, useState } from \"react\";\nimport { defaultProps } from \"./props.js\";\nimport Player from \"./Player.js\";\nconst Preview = lazy(() => import(\n  /* webpackChunkName: 'reactPlayerPreview' */\n  \"./Preview.js\"\n));\nconst customPlayers = [];\nconst createReactPlayer = (players, playerFallback) => {\n  const getActivePlayer = (src) => {\n    for (const player of [...customPlayers, ...players]) {\n      if (src && player.canPlay(src)) {\n        return player;\n      }\n    }\n    if (playerFallback) {\n      return playerFallback;\n    }\n    return null;\n  };\n  const ReactPlayer = React.forwardRef((_props, ref) => {\n    const props = { ...defaultProps, ..._props };\n    const { src, slot, className, style, width, height, fallback, wrapper } = props;\n    const [showPreview, setShowPreview] = useState(!!props.light);\n    useEffect(() => {\n      if (props.light) {\n        setShowPreview(true);\n      } else {\n        setShowPreview(false);\n      }\n    }, [props.light]);\n    const handleClickPreview = (e) => {\n      var _a;\n      setShowPreview(false);\n      (_a = props.onClickPreview) == null ? void 0 : _a.call(props, e);\n    };\n    const renderPreview = (src2) => {\n      if (!src2) return null;\n      const { light, playIcon, previewTabIndex, oEmbedUrl, previewAriaLabel } = props;\n      return /* @__PURE__ */ React.createElement(\n        Preview,\n        {\n          src: src2,\n          light,\n          playIcon,\n          previewTabIndex,\n          previewAriaLabel,\n          oEmbedUrl,\n          onClickPreview: handleClickPreview\n        }\n      );\n    };\n    const renderActivePlayer = (src2) => {\n      var _a, _b;\n      const player = getActivePlayer(src2);\n      if (!player) return null;\n      const { style: style2, width: width2, height: height2, wrapper: wrapper2 } = props;\n      const config = (_a = props.config) == null ? void 0 : _a[player.key];\n      return /* @__PURE__ */ React.createElement(\n        Player,\n        {\n          ...props,\n          ref,\n          activePlayer: (_b = player.player) != null ? _b : player,\n          slot: wrapper2 ? void 0 : slot,\n          className: wrapper2 ? void 0 : className,\n          style: wrapper2 ? { display: \"block\", width: \"100%\", height: \"100%\" } : { display: \"block\", width: width2, height: height2, ...style2 },\n          config\n        }\n      );\n    };\n    const Wrapper = wrapper == null ? ForwardChildren : wrapper;\n    const UniversalSuspense = fallback === false ? ForwardChildren : Suspense;\n    return /* @__PURE__ */ React.createElement(Wrapper, { slot, className, style: { width, height, ...style } }, /* @__PURE__ */ React.createElement(UniversalSuspense, { fallback }, showPreview ? renderPreview(src) : renderActivePlayer(src)));\n  });\n  ReactPlayer.displayName = \"ReactPlayer\";\n  ReactPlayer.addCustomPlayer = (player) => {\n    customPlayers.push(player);\n  };\n  ReactPlayer.removeCustomPlayers = () => {\n    customPlayers.length = 0;\n  };\n  ReactPlayer.canPlay = (src) => {\n    if (src) {\n      for (const Player2 of [...customPlayers, ...players]) {\n        if (Player2.canPlay(src)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  ReactPlayer.canEnablePIP = (src) => {\n    var _a;\n    if (src) {\n      for (const Player2 of [...customPlayers, ...players]) {\n        if (Player2.canPlay(src) && ((_a = Player2.canEnablePIP) == null ? void 0 : _a.call(Player2))) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  return ReactPlayer;\n};\nconst ForwardChildren = ({ children }) => children;\nexport {\n  createReactPlayer\n};\n", "const defaultProps = {\n  // Falsy values don't need to be defined\n  //\n  // native video attrs\n  // src: undefined,\n  // preload: undefined,\n  // crossOrigin: undefined,\n  // autoPlay: false,\n  // muted: false,\n  // loop: false,\n  // controls: false,\n  // playsInline: false,\n  width: \"320px\",\n  height: \"180px\",\n  // native video props\n  volume: 1,\n  playbackRate: 1,\n  // custom props\n  // playing: undefined,\n  // pip: false,\n  // light: false,\n  // fallback: null,\n  previewTabIndex: 0,\n  previewAriaLabel: \"\",\n  oEmbedUrl: \"https://noembed.com/embed?url={url}\"\n};\nexport {\n  defaultProps\n};\n", "import React, { useCallback, useEffect, useRef } from \"react\";\nconst Player = React.forwardRef((props, ref) => {\n  const { playing, pip } = props;\n  const Player2 = props.activePlayer;\n  const playerRef = useRef(null);\n  const startOnPlayRef = useRef(true);\n  useEffect(() => {\n    var _a, _b;\n    if (!playerRef.current) return;\n    if (playerRef.current.paused && playing === true) {\n      playerRef.current.play();\n    }\n    if (!playerRef.current.paused && playing === false) {\n      playerRef.current.pause();\n    }\n    playerRef.current.playbackRate = (_a = props.playbackRate) != null ? _a : 1;\n    playerRef.current.volume = (_b = props.volume) != null ? _b : 1;\n  });\n  useEffect(() => {\n    var _a, _b, _c, _d, _e;\n    if (!playerRef.current || !globalThis.document) return;\n    if (pip && !document.pictureInPictureElement) {\n      try {\n        (_b = (_a = playerRef.current).requestPictureInPicture) == null ? void 0 : _b.call(_a);\n      } catch (err) {\n      }\n    }\n    if (!pip && document.pictureInPictureElement) {\n      try {\n        (_d = (_c = playerRef.current).exitPictureInPicture) == null ? void 0 : _d.call(_c);\n        (_e = document.exitPictureInPicture) == null ? void 0 : _e.call(document);\n      } catch (err) {\n      }\n    }\n  }, [pip]);\n  const handleLoadStart = (event) => {\n    var _a, _b;\n    startOnPlayRef.current = true;\n    (_a = props.onReady) == null ? void 0 : _a.call(props);\n    (_b = props.onLoadStart) == null ? void 0 : _b.call(props, event);\n  };\n  const handlePlay = (event) => {\n    var _a, _b;\n    if (startOnPlayRef.current) {\n      startOnPlayRef.current = false;\n      (_a = props.onStart) == null ? void 0 : _a.call(props, event);\n    }\n    (_b = props.onPlay) == null ? void 0 : _b.call(props, event);\n  };\n  if (!Player2) {\n    return null;\n  }\n  const eventProps = {};\n  for (const key in props) {\n    if (key.startsWith(\"on\")) {\n      eventProps[key] = props[key];\n    }\n  }\n  return /* @__PURE__ */ React.createElement(\n    Player2,\n    {\n      ...eventProps,\n      style: props.style,\n      className: props.className,\n      slot: props.slot,\n      ref: useCallback(\n        (node) => {\n          playerRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      ),\n      src: props.src,\n      crossOrigin: props.crossOrigin,\n      preload: props.preload,\n      controls: props.controls,\n      muted: props.muted,\n      autoPlay: props.autoPlay,\n      loop: props.loop,\n      playsInline: props.playsInline,\n      config: props.config,\n      onLoadStart: handleLoadStart,\n      onPlay: handlePlay\n    },\n    props.children\n  );\n});\nPlayer.displayName = \"Player\";\nvar Player_default = Player;\nexport {\n  Player_default as default\n};\n", "\"use client\";\nimport players from \"./players.js\";\nimport { createReactPlayer } from \"./ReactPlayer.js\";\nconst fallback = players[players.length - 1];\nvar src_default = createReactPlayer(players, fallback);\nexport {\n  src_default as default\n};\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,gBAAqB;;;ACArB,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,cAAc,CAAC,KAAK,SAAS;AACjC,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAW,QAAQ,KAAK;AACtB,UAAI,OAAO,SAAS,YAAY,YAAY,MAAM,IAAI,GAAG;AACvD,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,KAAK,IAAI,GAAG;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,KAAK,GAAG;AACjB;AACA,IAAM,UAAU;AAAA,EACd,MAAM,CAAC,QAAQ,YAAY,KAAK,CAAC,MAAM,iBAAiB,KAAK,CAAC,KAAK,iBAAiB,KAAK,CAAC,CAAC;AAAA,EAC3F,KAAK,CAAC,QAAQ,YAAY,KAAK,CAAC,MAAM,eAAe,KAAK,CAAC,CAAC;AAAA,EAC5D,MAAM,CAAC,QAAQ,YAAY,KAAK,CAAC,MAAM,gBAAgB,KAAK,CAAC,CAAC;AAAA,EAC9D,KAAK,CAAC,QAAQ,cAAc,KAAK,GAAG;AAAA,EACpC,SAAS,CAAC,QAAQ,kBAAkB,KAAK,GAAG;AAAA,EAC5C,OAAO,CAAC,QAAQ,gBAAgB,KAAK,GAAG,KAAK,CAAC,iBAAiB,KAAK,GAAG,KAAK,CAAC,eAAe,KAAK,GAAG;AAAA,EACpG,QAAQ,CAAC,QAAQ,iBAAiB,KAAK,GAAG;AAAA,EAC1C,SAAS,CAAC,QAAQ,kBAAkB,KAAK,GAAG;AAAA,EAC5C,QAAQ,CAAC,QAAQ,iBAAiB,KAAK,GAAG;AAAA,EAC1C,QAAQ,CAAC,QAAQ,iBAAiB,KAAK,GAAG;AAC5C;;;ACpCA,mBAAkB;AAElB,IAAM,aAAa,aAAAC,QAAM,WAAW,CAAC,OAAO,QAAQ;AAClD,QAAM,QAAQ,iBAAiB,KAAK,GAAG,MAAM,GAAG,EAAE,IAAI,UAAU;AAChE,SAAuB,aAAAA,QAAM,cAAc,OAAO,EAAE,GAAG,OAAO,IAAI,GAAG,MAAM,QAAQ;AACrF,CAAC;AACD,IAAI,qBAAqB;;;AFHzB,IAAM,UAAU;AAAA,EACd;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,YAAQ;AAAA,MACN,MAAM;AAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,QAAQ;AAAA,IACjB,cAAc,MAAM;AAAA,IACpB,QAAQ;AAAA,EACV;AACF;AACA,IAAI,kBAAkB;;;AGtHtB,IAAAC,gBAA2D;;;ACA3D,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnB,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,QAAQ;AAAA,EACR,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,WAAW;AACb;;;ACzBA,IAAAC,gBAAsD;AACtD,IAAM,SAAS,cAAAC,QAAM,WAAW,CAAC,OAAO,QAAQ;AAC9C,QAAM,EAAE,SAAS,IAAI,IAAI;AACzB,QAAM,UAAU,MAAM;AACtB,QAAM,gBAAY,sBAAO,IAAI;AAC7B,QAAM,qBAAiB,sBAAO,IAAI;AAClC,+BAAU,MAAM;AACd,QAAI,IAAI;AACR,QAAI,CAAC,UAAU,QAAS;AACxB,QAAI,UAAU,QAAQ,UAAU,YAAY,MAAM;AAChD,gBAAU,QAAQ,KAAK;AAAA,IACzB;AACA,QAAI,CAAC,UAAU,QAAQ,UAAU,YAAY,OAAO;AAClD,gBAAU,QAAQ,MAAM;AAAA,IAC1B;AACA,cAAU,QAAQ,gBAAgB,KAAK,MAAM,iBAAiB,OAAO,KAAK;AAC1E,cAAU,QAAQ,UAAU,KAAK,MAAM,WAAW,OAAO,KAAK;AAAA,EAChE,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,IAAI,IAAI,IAAI,IAAI;AACpB,QAAI,CAAC,UAAU,WAAW,CAAC,WAAW,SAAU;AAChD,QAAI,OAAO,CAAC,SAAS,yBAAyB;AAC5C,UAAI;AACF,SAAC,MAAM,KAAK,UAAU,SAAS,4BAA4B,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,MACvF,SAAS,KAAK;AAAA,MACd;AAAA,IACF;AACA,QAAI,CAAC,OAAO,SAAS,yBAAyB;AAC5C,UAAI;AACF,SAAC,MAAM,KAAK,UAAU,SAAS,yBAAyB,OAAO,SAAS,GAAG,KAAK,EAAE;AAClF,SAAC,KAAK,SAAS,yBAAyB,OAAO,SAAS,GAAG,KAAK,QAAQ;AAAA,MAC1E,SAAS,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,QAAM,kBAAkB,CAAC,UAAU;AACjC,QAAI,IAAI;AACR,mBAAe,UAAU;AACzB,KAAC,KAAK,MAAM,YAAY,OAAO,SAAS,GAAG,KAAK,KAAK;AACrD,KAAC,KAAK,MAAM,gBAAgB,OAAO,SAAS,GAAG,KAAK,OAAO,KAAK;AAAA,EAClE;AACA,QAAM,aAAa,CAAC,UAAU;AAC5B,QAAI,IAAI;AACR,QAAI,eAAe,SAAS;AAC1B,qBAAe,UAAU;AACzB,OAAC,KAAK,MAAM,YAAY,OAAO,SAAS,GAAG,KAAK,OAAO,KAAK;AAAA,IAC9D;AACA,KAAC,KAAK,MAAM,WAAW,OAAO,SAAS,GAAG,KAAK,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,OAAO,OAAO;AACvB,QAAI,IAAI,WAAW,IAAI,GAAG;AACxB,iBAAW,GAAG,IAAI,MAAM,GAAG;AAAA,IAC7B;AAAA,EACF;AACA,SAAuB,cAAAA,QAAM;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,OAAO,MAAM;AAAA,MACb,WAAW,MAAM;AAAA,MACjB,MAAM,MAAM;AAAA,MACZ,SAAK;AAAA,QACH,CAAC,SAAS;AACR,oBAAU,UAAU;AACpB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,aAAa,MAAM;AAAA,MACnB,SAAS,MAAM;AAAA,MACf,UAAU,MAAM;AAAA,MAChB,OAAO,MAAM;AAAA,MACb,UAAU,MAAM;AAAA,MAChB,MAAM,MAAM;AAAA,MACZ,aAAa,MAAM;AAAA,MACnB,QAAQ,MAAM;AAAA,MACd,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,EACR;AACF,CAAC;AACD,OAAO,cAAc;AACrB,IAAI,iBAAiB;;;AFzFrB,IAAM,cAAU,oBAAK,MAAM;AAAA;AAAA,EAEzB;AACF,CAAC;AACD,IAAM,gBAAgB,CAAC;AACvB,IAAM,oBAAoB,CAAC,SAAS,mBAAmB;AACrD,QAAM,kBAAkB,CAAC,QAAQ;AAC/B,eAAW,UAAU,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACnD,UAAI,OAAO,OAAO,QAAQ,GAAG,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,cAAAC,QAAM,WAAW,CAAC,QAAQ,QAAQ;AACpD,UAAM,QAAQ,EAAE,GAAG,cAAc,GAAG,OAAO;AAC3C,UAAM,EAAE,KAAK,MAAM,WAAW,OAAO,OAAO,QAAQ,UAAAC,WAAU,QAAQ,IAAI;AAC1E,UAAM,CAAC,aAAa,cAAc,QAAI,wBAAS,CAAC,CAAC,MAAM,KAAK;AAC5D,iCAAU,MAAM;AACd,UAAI,MAAM,OAAO;AACf,uBAAe,IAAI;AAAA,MACrB,OAAO;AACL,uBAAe,KAAK;AAAA,MACtB;AAAA,IACF,GAAG,CAAC,MAAM,KAAK,CAAC;AAChB,UAAM,qBAAqB,CAAC,MAAM;AAChC,UAAI;AACJ,qBAAe,KAAK;AACpB,OAAC,KAAK,MAAM,mBAAmB,OAAO,SAAS,GAAG,KAAK,OAAO,CAAC;AAAA,IACjE;AACA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,UAAI,CAAC,KAAM,QAAO;AAClB,YAAM,EAAE,OAAO,UAAU,iBAAiB,WAAW,iBAAiB,IAAI;AAC1E,aAAuB,cAAAD,QAAM;AAAA,QAC3B;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,UAAM,qBAAqB,CAAC,SAAS;AACnC,UAAI,IAAI;AACR,YAAM,SAAS,gBAAgB,IAAI;AACnC,UAAI,CAAC,OAAQ,QAAO;AACpB,YAAM,EAAE,OAAO,QAAQ,OAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,IAAI;AAC7E,YAAM,UAAU,KAAK,MAAM,WAAW,OAAO,SAAS,GAAG,OAAO,GAAG;AACnE,aAAuB,cAAAA,QAAM;AAAA,QAC3B;AAAA,QACA;AAAA,UACE,GAAG;AAAA,UACH;AAAA,UACA,eAAe,KAAK,OAAO,WAAW,OAAO,KAAK;AAAA,UAClD,MAAM,WAAW,SAAS;AAAA,UAC1B,WAAW,WAAW,SAAS;AAAA,UAC/B,OAAO,WAAW,EAAE,SAAS,SAAS,OAAO,QAAQ,QAAQ,OAAO,IAAI,EAAE,SAAS,SAAS,OAAO,QAAQ,QAAQ,SAAS,GAAG,OAAO;AAAA,UACtI;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,WAAW,OAAO,kBAAkB;AACpD,UAAM,oBAAoBC,cAAa,QAAQ,kBAAkB;AACjE,WAAuB,cAAAD,QAAM,cAAc,SAAS,EAAE,MAAM,WAAW,OAAO,EAAE,OAAO,QAAQ,GAAG,MAAM,EAAE,GAAmB,cAAAA,QAAM,cAAc,mBAAmB,EAAE,UAAAC,UAAS,GAAG,cAAc,cAAc,GAAG,IAAI,mBAAmB,GAAG,CAAC,CAAC;AAAA,EAC/O,CAAC;AACD,cAAY,cAAc;AAC1B,cAAY,kBAAkB,CAAC,WAAW;AACxC,kBAAc,KAAK,MAAM;AAAA,EAC3B;AACA,cAAY,sBAAsB,MAAM;AACtC,kBAAc,SAAS;AAAA,EACzB;AACA,cAAY,UAAU,CAAC,QAAQ;AAC7B,QAAI,KAAK;AACP,iBAAW,WAAW,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACpD,YAAI,QAAQ,QAAQ,GAAG,GAAG;AACxB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,cAAY,eAAe,CAAC,QAAQ;AAClC,QAAI;AACJ,QAAI,KAAK;AACP,iBAAW,WAAW,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACpD,YAAI,QAAQ,QAAQ,GAAG,OAAO,KAAK,QAAQ,iBAAiB,OAAO,SAAS,GAAG,KAAK,OAAO,IAAI;AAC7F,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC,EAAE,SAAS,MAAM;;;AGtG1C,IAAM,WAAW,gBAAQ,gBAAQ,SAAS,CAAC;AAC3C,IAAI,cAAc,kBAAkB,iBAAS,QAAQ;", "names": ["import_react", "React", "import_react", "import_react", "React", "React", "fallback"]}