{"version": 3, "sources": ["../../youtube-video-element/dist/react.js", "../../youtube-video-element/dist/youtube-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./youtube-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"youtube-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "const EMBED_BASE = \"https://www.youtube.com/embed\";\nconst EMBED_BASE_NOCOOKIE = \"https://www.youtube-nocookie.com/embed\";\nconst API_URL = \"https://www.youtube.com/iframe_api\";\nconst API_GLOBAL = \"YT\";\nconst API_GLOBAL_READY = \"onYouTubeIframeAPIReady\";\nconst VIDEO_MATCH_SRC = /(?:youtu\\.be\\/|youtube(?:-nocookie)?\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=|shorts\\/|live\\/))((\\w|-){11})/;\nconst PLAYLIST_MATCH_SRC = /(?:youtu\\.be\\/|youtube(?:-nocookie)?\\.com\\/.*?[?&]list=)([\\w_-]+)/;\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n        position: relative;\n        min-width: 300px;\n        min-height: 150px;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  if (!attrs.src) return;\n  const embedBase = attrs.src.includes(\"-nocookie\") ? EMBED_BASE_NOCOOKIE : EMBED_BASE;\n  const params = {\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    loop: attrs.loop,\n    mute: attrs.muted,\n    playsinline: attrs.playsinline,\n    preload: attrs.preload ?? \"metadata\",\n    // https://developers.google.com/youtube/player_parameters#Parameters\n    // origin: globalThis.location?.origin,\n    enablejsapi: 1,\n    showinfo: 0,\n    rel: 0,\n    iv_load_policy: 3,\n    modestbranding: 1,\n    ...props.config\n  };\n  if (VIDEO_MATCH_SRC.test(attrs.src)) {\n    const matches2 = attrs.src.match(VIDEO_MATCH_SRC);\n    const srcId = matches2 && matches2[1];\n    return `${embedBase}/${srcId}?${serialize(params)}`;\n  }\n  const matches = attrs.src.match(PLAYLIST_MATCH_SRC);\n  const playlistId = matches && matches[1];\n  const extendedParams = {\n    listType: \"playlist\",\n    list: playlistId,\n    ...params\n  };\n  return `${embedBase}?${serialize(extendedParams)}`;\n}\nclass YoutubeVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"autoplay\",\n    \"controls\",\n    \"crossorigin\",\n    \"loop\",\n    \"muted\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #readyState = 0;\n  #seeking = false;\n  #seekComplete;\n  isLoaded = false;\n  #error = null;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n      this.isLoaded = false;\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#readyState = 0;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      oldApi == null ? void 0 : oldApi.destroy();\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    let attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    const YT = await loadScript(API_URL, API_GLOBAL, API_GLOBAL_READY);\n    this.api = new YT.Player(iframe, {\n      events: {\n        onReady: () => {\n          this.#readyState = 1;\n          this.dispatchEvent(new Event(\"loadedmetadata\"));\n          this.dispatchEvent(new Event(\"durationchange\"));\n          this.dispatchEvent(new Event(\"volumechange\"));\n          this.dispatchEvent(new Event(\"loadcomplete\"));\n          this.isLoaded = true;\n          this.loadComplete.resolve();\n        },\n        onError: (error) => {\n          console.error(error);\n          this.#error = {\n            code: error.data,\n            message: `YouTube iframe player error #${error.data}; visit https://developers.google.com/youtube/iframe_api_reference#onError for the full error message.`\n          };\n          this.dispatchEvent(new Event(\"error\"));\n        }\n      }\n    });\n    let playFired = false;\n    this.api.addEventListener(\"onStateChange\", (event) => {\n      var _a;\n      const state = event.data;\n      if (state === YT.PlayerState.PLAYING || state === YT.PlayerState.BUFFERING) {\n        if (!playFired) {\n          playFired = true;\n          this.dispatchEvent(new Event(\"play\"));\n        }\n      }\n      if (state === YT.PlayerState.PLAYING) {\n        if (this.seeking) {\n          this.#seeking = false;\n          (_a = this.#seekComplete) == null ? void 0 : _a.resolve();\n          this.dispatchEvent(new Event(\"seeked\"));\n        }\n        this.#readyState = 3;\n        this.dispatchEvent(new Event(\"playing\"));\n      } else if (state === YT.PlayerState.PAUSED) {\n        const diff = Math.abs(this.currentTime - lastCurrentTime);\n        if (!this.seeking && diff > 0.1) {\n          this.#seeking = true;\n          this.dispatchEvent(new Event(\"seeking\"));\n        }\n        playFired = false;\n        this.dispatchEvent(new Event(\"pause\"));\n      }\n      if (state === YT.PlayerState.ENDED) {\n        playFired = false;\n        this.dispatchEvent(new Event(\"pause\"));\n        this.dispatchEvent(new Event(\"ended\"));\n        if (this.loop) {\n          this.play();\n        }\n      }\n    });\n    this.api.addEventListener(\"onPlaybackRateChange\", () => {\n      this.dispatchEvent(new Event(\"ratechange\"));\n    });\n    this.api.addEventListener(\"onVolumeChange\", () => {\n      this.dispatchEvent(new Event(\"volumechange\"));\n    });\n    this.api.addEventListener(\"onVideoProgress\", () => {\n      this.dispatchEvent(new Event(\"timeupdate\"));\n    });\n    await this.loadComplete;\n    let lastCurrentTime = 0;\n    setInterval(() => {\n      var _a;\n      const diff = Math.abs(this.currentTime - lastCurrentTime);\n      const bufferedEnd = this.buffered.end(this.buffered.length - 1);\n      if (this.seeking && bufferedEnd > 0.1) {\n        this.#seeking = false;\n        (_a = this.#seekComplete) == null ? void 0 : _a.resolve();\n        this.dispatchEvent(new Event(\"seeked\"));\n      } else if (!this.seeking && diff > 0.1) {\n        this.#seeking = true;\n        this.dispatchEvent(new Event(\"seeking\"));\n      }\n      lastCurrentTime = this.currentTime;\n    }, 50);\n    let lastBufferedEnd;\n    const progressInterval = setInterval(() => {\n      const bufferedEnd = this.buffered.end(this.buffered.length - 1);\n      if (bufferedEnd >= this.duration) {\n        clearInterval(progressInterval);\n        this.#readyState = 4;\n      }\n      if (lastBufferedEnd != bufferedEnd) {\n        lastBufferedEnd = bufferedEnd;\n        this.dispatchEvent(new Event(\"progress\"));\n      }\n    }, 100);\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\":\n      case \"autoplay\":\n      case \"controls\":\n      case \"loop\":\n      case \"playsinline\": {\n        this.load();\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#seekComplete = null;\n    await this.loadComplete;\n    (_a = this.api) == null ? void 0 : _a.playVideo();\n    return createPlayPromise(this);\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pauseVideo();\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    if (this.src == val) return;\n    this.setAttribute(\"src\", val);\n  }\n  get error() {\n    return this.#error;\n  }\n  /* onStateChange\n    -1 (unstarted)\n    0 (ended)\n    1 (playing)\n    2 (paused)\n    3 (buffering)\n    5 (video cued).\n  */\n  get paused() {\n    var _a, _b;\n    if (!this.isLoaded) return !this.autoplay;\n    return [-1, 0, 2, 5].includes((_b = (_a = this.api) == null ? void 0 : _a.getPlayerState) == null ? void 0 : _b.call(_a));\n  }\n  get duration() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getDuration) == null ? void 0 : _b.call(_a)) ?? NaN;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get buffered() {\n    var _a, _b;\n    if (!this.isLoaded) return createTimeRanges();\n    const progress = ((_a = this.api) == null ? void 0 : _a.getVideoLoadedFraction()) * ((_b = this.api) == null ? void 0 : _b.getDuration());\n    if (progress > 0) {\n      return createTimeRanges(0, progress);\n    }\n    return createTimeRanges();\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getCurrentTime) == null ? void 0 : _b.call(_a)) ?? 0;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#seekComplete = new PublicPromise();\n    this.loadComplete.then(() => {\n      var _a, _b;\n      (_a = this.api) == null ? void 0 : _a.seekTo(val, true);\n      if (this.paused) {\n        (_b = this.#seekComplete) == null ? void 0 : _b.then(() => {\n          var _a2;\n          if (!this.#seekComplete) return;\n          (_a2 = this.api) == null ? void 0 : _a2.pauseVideo();\n        });\n      }\n    });\n  }\n  set defaultMuted(val) {\n    if (this.defaultMuted == val) return;\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  set muted(val) {\n    if (this.muted == val) return;\n    this.loadComplete.then(() => {\n      var _a, _b;\n      val ? (_a = this.api) == null ? void 0 : _a.mute() : (_b = this.api) == null ? void 0 : _b.unMute();\n    });\n  }\n  get muted() {\n    var _a, _b;\n    if (!this.isLoaded) return this.defaultMuted;\n    return (_b = (_a = this.api) == null ? void 0 : _a.isMuted) == null ? void 0 : _b.call(_a);\n  }\n  get playbackRate() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getPlaybackRate) == null ? void 0 : _b.call(_a)) ?? 1;\n  }\n  set playbackRate(val) {\n    if (this.playbackRate == val) return;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setPlaybackRate(val);\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    if (this.playsInline == val) return;\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  get poster() {\n    return this.getAttribute(\"poster\");\n  }\n  set poster(val) {\n    if (this.poster == val) return;\n    this.setAttribute(\"poster\", `${val}`);\n  }\n  set volume(val) {\n    if (this.volume == val) return;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setVolume(val * 100);\n    });\n  }\n  get volume() {\n    var _a;\n    if (!this.isLoaded) return 1;\n    return ((_a = this.api) == null ? void 0 : _a.getVolume()) / 100;\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName, readyFnName) {\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (globalName && self[globalName]) {\n    await delay(0);\n    return self[globalName];\n  }\n  return loadScriptCache[src] = new Promise(function(resolve, reject) {\n    const script = document.createElement(\"script\");\n    script.src = src;\n    const ready = () => resolve(self[globalName]);\n    if (readyFnName) self[readyFnName] = ready;\n    script.onload = () => !readyFnName && ready();\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nconst delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nfunction promisify(fn) {\n  return (...args) => new Promise((resolve) => {\n    fn(...args, (...res) => {\n      if (res.length > 1) resolve(res);\n      else resolve(res[0]);\n    });\n  });\n}\nfunction createPlayPromise(player) {\n  return promisify((event, cb) => {\n    let fn;\n    player.addEventListener(\n      event,\n      fn = () => {\n        player.removeEventListener(event, fn);\n        cb();\n      }\n    );\n  })(\"playing\");\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"youtube-video\")) {\n  globalThis.customElements.define(\"youtube-video\", YoutubeVideoElement);\n}\nvar youtube_video_element_default = YoutubeVideoElement;\nexport {\n  youtube_video_element_default as default\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACHlB,IAAM,aAAa;AACnB,IAAM,sBAAsB;AAC5B,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,SAAS,gBAAgB,OAAO,QAAQ,CAAC,GAAG;AAC1C,QAAM,cAAc;AAAA,IAClB,KAAK,mBAAmB,OAAO,KAAK;AAAA,IACpC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ;AAChB,gBAAY,aAAa,IAAI,KAAK,UAAU,MAAM,MAAM;AAAA,EAC1D;AACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAeS,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAG7C;AACA,SAAS,mBAAmB,OAAO,OAAO;AACxC,MAAI,CAAC,MAAM,IAAK;AAChB,QAAM,YAAY,MAAM,IAAI,SAAS,WAAW,IAAI,sBAAsB;AAC1E,QAAM,SAAS;AAAA;AAAA,IAEb,UAAU,MAAM,aAAa,KAAK,OAAO;AAAA,IACzC,UAAU,MAAM;AAAA,IAChB,MAAM,MAAM;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM,WAAW;AAAA;AAAA;AAAA,IAG1B,aAAa;AAAA,IACb,UAAU;AAAA,IACV,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,GAAG,MAAM;AAAA,EACX;AACA,MAAI,gBAAgB,KAAK,MAAM,GAAG,GAAG;AACnC,UAAM,WAAW,MAAM,IAAI,MAAM,eAAe;AAChD,UAAM,QAAQ,YAAY,SAAS,CAAC;AACpC,WAAO,GAAG,SAAS,IAAI,KAAK,IAAI,UAAU,MAAM,CAAC;AAAA,EACnD;AACA,QAAM,UAAU,MAAM,IAAI,MAAM,kBAAkB;AAClD,QAAM,aAAa,WAAW,QAAQ,CAAC;AACvC,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACA,SAAO,GAAG,SAAS,IAAI,UAAU,cAAc,CAAC;AAClD;AACA,IAAM,sBAAN,eAAmC,WAAW,eAAe,MAAM;AACnE,GAAG;AAAA,EACD,OAAO,kBAAkB;AAAA,EACzB,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,EAC1C,OAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe,IAAI,cAAc;AAAA,EACjC;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,WAAW;AAAA,EACX;AAAA,EACA,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,eAAgB;AACzB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,IACpC;AACA,UAAM,cAAc,CAAC,KAAK;AAC1B,QAAI,KAAK,YAAY;AACnB,WAAK,eAAe,IAAI,cAAc;AACtC,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,aAAa;AAClB,WAAO,KAAK,iBAAiB,QAAQ,QAAQ;AAC7C,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC,QAAI,SAAS,KAAK;AAClB,SAAK,MAAM;AACX,QAAI,CAAC,KAAK,KAAK;AACb,gBAAU,OAAO,SAAS,OAAO,QAAQ;AACzC;AAAA,IACF;AACA,SAAK,cAAc,IAAI,MAAM,WAAW,CAAC;AACzC,QAAI,SAAS,KAAK,WAAW,cAAc,QAAQ;AACnD,QAAI,QAAQ,qBAAqB,KAAK,UAAU;AAChD,QAAI,eAAe,QAAQ;AACzB,WAAK,UAAU,KAAK,MAAM,OAAO,aAAa,aAAa,KAAK,IAAI;AAAA,IACtE;AACA,QAAI,EAAE,UAAU,OAAO,SAAS,OAAO,QAAQ,OAAO,QAAQ,mBAAmB,OAAO,IAAI,GAAG;AAC7F,WAAK,WAAW,YAAY,gBAAgB,OAAO,IAAI;AACvD,eAAS,KAAK,WAAW,cAAc,QAAQ;AAAA,IACjD;AACA,UAAM,KAAK,MAAM,WAAW,SAAS,YAAY,gBAAgB;AACjE,SAAK,MAAM,IAAI,GAAG,OAAO,QAAQ;AAAA,MAC/B,QAAQ;AAAA,QACN,SAAS,MAAM;AACb,eAAK,cAAc;AACnB,eAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,eAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,eAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C,eAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C,eAAK,WAAW;AAChB,eAAK,aAAa,QAAQ;AAAA,QAC5B;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,kBAAQ,MAAM,KAAK;AACnB,eAAK,SAAS;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,SAAS,gCAAgC,MAAM,IAAI;AAAA,UACrD;AACA,eAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,YAAY;AAChB,SAAK,IAAI,iBAAiB,iBAAiB,CAAC,UAAU;AACpD,UAAI;AACJ,YAAM,QAAQ,MAAM;AACpB,UAAI,UAAU,GAAG,YAAY,WAAW,UAAU,GAAG,YAAY,WAAW;AAC1E,YAAI,CAAC,WAAW;AACd,sBAAY;AACZ,eAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AAAA,QACtC;AAAA,MACF;AACA,UAAI,UAAU,GAAG,YAAY,SAAS;AACpC,YAAI,KAAK,SAAS;AAChB,eAAK,WAAW;AAChB,WAAC,KAAK,KAAK,kBAAkB,OAAO,SAAS,GAAG,QAAQ;AACxD,eAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,QACxC;AACA,aAAK,cAAc;AACnB,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,MACzC,WAAW,UAAU,GAAG,YAAY,QAAQ;AAC1C,cAAM,OAAO,KAAK,IAAI,KAAK,cAAc,eAAe;AACxD,YAAI,CAAC,KAAK,WAAW,OAAO,KAAK;AAC/B,eAAK,WAAW;AAChB,eAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,QACzC;AACA,oBAAY;AACZ,aAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,MACvC;AACA,UAAI,UAAU,GAAG,YAAY,OAAO;AAClC,oBAAY;AACZ,aAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AACrC,aAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AACrC,YAAI,KAAK,MAAM;AACb,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,IAAI,iBAAiB,wBAAwB,MAAM;AACtD,WAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,IAC5C,CAAC;AACD,SAAK,IAAI,iBAAiB,kBAAkB,MAAM;AAChD,WAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,SAAK,IAAI,iBAAiB,mBAAmB,MAAM;AACjD,WAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAAA,IAC5C,CAAC;AACD,UAAM,KAAK;AACX,QAAI,kBAAkB;AACtB,gBAAY,MAAM;AAChB,UAAI;AACJ,YAAM,OAAO,KAAK,IAAI,KAAK,cAAc,eAAe;AACxD,YAAM,cAAc,KAAK,SAAS,IAAI,KAAK,SAAS,SAAS,CAAC;AAC9D,UAAI,KAAK,WAAW,cAAc,KAAK;AACrC,aAAK,WAAW;AAChB,SAAC,KAAK,KAAK,kBAAkB,OAAO,SAAS,GAAG,QAAQ;AACxD,aAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAA,MACxC,WAAW,CAAC,KAAK,WAAW,OAAO,KAAK;AACtC,aAAK,WAAW;AAChB,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,MACzC;AACA,wBAAkB,KAAK;AAAA,IACzB,GAAG,EAAE;AACL,QAAI;AACJ,UAAM,mBAAmB,YAAY,MAAM;AACzC,YAAM,cAAc,KAAK,SAAS,IAAI,KAAK,SAAS,SAAS,CAAC;AAC9D,UAAI,eAAe,KAAK,UAAU;AAChC,sBAAc,gBAAgB;AAC9B,aAAK,cAAc;AAAA,MACrB;AACA,UAAI,mBAAmB,aAAa;AAClC,0BAAkB;AAClB,aAAK,cAAc,IAAI,MAAM,UAAU,CAAC;AAAA,MAC1C;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA,EACA,MAAM,yBAAyB,UAAU,UAAU,UAAU;AAC3D,QAAI,aAAa,SAAU;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,eAAe;AAClB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,QAAI;AACJ,SAAK,gBAAgB;AACrB,UAAM,KAAK;AACX,KAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,UAAU;AAChD,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI;AACJ,UAAM,KAAK;AACX,YAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,WAAW;AAAA,EAC1D;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,IAAI,KAAK;AACX,QAAI,KAAK,OAAO,IAAK;AACrB,SAAK,aAAa,OAAO,GAAG;AAAA,EAC9B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,SAAS;AACX,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,SAAU,QAAO,CAAC,KAAK;AACjC,WAAO,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,UAAU,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,mBAAmB,OAAO,SAAS,GAAG,KAAK,EAAE,CAAC;AAAA,EAC1H;AAAA,EACA,IAAI,WAAW;AACb,QAAI,IAAI;AACR,aAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,gBAAgB,OAAO,SAAS,GAAG,KAAK,EAAE,MAAM;AAAA,EACtG;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,WAAW;AACb,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,SAAU,QAAO,iBAAiB;AAC5C,UAAM,aAAa,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,uBAAuB,OAAO,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,YAAY;AACvI,QAAI,WAAW,GAAG;AAChB,aAAO,iBAAiB,GAAG,QAAQ;AAAA,IACrC;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,KAAK,YAAY,IAAK;AAC1B,SAAK,gBAAgB,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC/C;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,IAAI;AACR,aAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,mBAAmB,OAAO,SAAS,GAAG,KAAK,EAAE,MAAM;AAAA,EACzG;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK,eAAe,IAAK;AAC7B,SAAK,gBAAgB,IAAI,cAAc;AACvC,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI,IAAI;AACR,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,OAAO,KAAK,IAAI;AACtD,UAAI,KAAK,QAAQ;AACf,SAAC,KAAK,KAAK,kBAAkB,OAAO,SAAS,GAAG,KAAK,MAAM;AACzD,cAAI;AACJ,cAAI,CAAC,KAAK,cAAe;AACzB,WAAC,MAAM,KAAK,QAAQ,OAAO,SAAS,IAAI,WAAW;AAAA,QACrD,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,QAAI,KAAK,gBAAgB,IAAK;AAC9B,SAAK,gBAAgB,SAAS,QAAQ,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa,MAAM;AAAA,EACjC;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,QAAI,KAAK,QAAQ,IAAK;AACtB,SAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC3C;AAAA,EACA,IAAI,MAAM,KAAK;AACb,QAAI,KAAK,SAAS,IAAK;AACvB,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI,IAAI;AACR,aAAO,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,KAAK,KAAK,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,OAAO;AAAA,IACpG,CAAC;AAAA,EACH;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,SAAU,QAAO,KAAK;AAChC,YAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,YAAY,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,EAC3F;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,IAAI;AACR,aAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,oBAAoB,OAAO,SAAS,GAAG,KAAK,EAAE,MAAM;AAAA,EAC1G;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,QAAI,KAAK,gBAAgB,IAAK;AAC9B,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,gBAAgB,GAAG;AAAA,IAC3D,CAAC;AAAA,EACH;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,QAAI,KAAK,eAAe,IAAK;AAC7B,SAAK,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AAAA,EAClD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,aAAa,QAAQ;AAAA,EACnC;AAAA,EACA,IAAI,OAAO,KAAK;AACd,QAAI,KAAK,UAAU,IAAK;AACxB,SAAK,aAAa,UAAU,GAAG,GAAG,EAAE;AAAA,EACtC;AAAA,EACA,IAAI,OAAO,KAAK;AACd,QAAI,KAAK,UAAU,IAAK;AACxB,SAAK,aAAa,KAAK,MAAM;AAC3B,UAAI;AACJ,OAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,UAAU,MAAM,GAAG;AAAA,IAC3D,CAAC;AAAA,EACH;AAAA,EACA,IAAI,SAAS;AACX,QAAI;AACJ,QAAI,CAAC,KAAK,SAAU,QAAO;AAC3B,aAAS,KAAK,KAAK,QAAQ,OAAO,SAAS,GAAG,UAAU,KAAK;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAM,QAAQ,KAAK,IAAI;AACvB,aAAO,KAAK,IAAI;AAChB,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,WAAW,GAAG,CAAC;AAAA,QACxC,SAAQ,IAAI,WAAW,GAAG,CAAC,KAAK,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ;AACtJ;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,IAAI,gBAAgB,aAAa,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,QAAQ,QAAQ,QAAQ,GAAI,GAAE,GAAG,IAAI;AAAA,aAChC,QAAQ,MAAO,GAAE,GAAG,IAAI;AAAA,aACxB,OAAO,KAAM,GAAE,GAAG,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,cAAc;AAC7B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC;AACzB,eAAe,WAAW,KAAK,YAAY,aAAa;AACtD,MAAI,gBAAgB,GAAG,EAAG,QAAO,gBAAgB,GAAG;AACpD,MAAI,cAAc,KAAK,UAAU,GAAG;AAClC,UAAM,MAAM,CAAC;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,SAAO,gBAAgB,GAAG,IAAI,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAClE,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,UAAM,QAAQ,MAAM,QAAQ,KAAK,UAAU,CAAC;AAC5C,QAAI,YAAa,MAAK,WAAW,IAAI;AACrC,WAAO,SAAS,MAAM,CAAC,eAAe,MAAM;AAC5C,WAAO,UAAU;AACjB,aAAS,KAAK,OAAO,MAAM;AAAA,EAC7B,CAAC;AACH;AACA,IAAM,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AACtE,SAAS,UAAU,IAAI;AACrB,SAAO,IAAI,SAAS,IAAI,QAAQ,CAAC,YAAY;AAC3C,OAAG,GAAG,MAAM,IAAI,QAAQ;AACtB,UAAI,IAAI,SAAS,EAAG,SAAQ,GAAG;AAAA,UAC1B,SAAQ,IAAI,CAAC,CAAC;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,UAAU,CAAC,OAAO,OAAO;AAC9B,QAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA,KAAK,MAAM;AACT,eAAO,oBAAoB,OAAO,EAAE;AACpC,WAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,CAAC,EAAE,SAAS;AACd;AACA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAC7B,GAAG;AACD,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,iBAAiB,OAAO,KAAK;AACpC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,oBAAoB,KAAK;AAAA,EAClC,WAAW,SAAS,QAAQ,OAAO,QAAQ,UAAU,KAAK,QAAQ,GAAG;AACnE,WAAO,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,EACrC;AACA,SAAO,oBAAoB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;AAC3C;AACA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,iBAAiB,QAAQ;AAAA,IAC9B,OAAO;AAAA,MACL,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACH,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,eAAe,GAAG;AAChF,aAAW,eAAe,OAAO,iBAAiB,mBAAmB;AACvE;AACA,IAAI,gCAAgC;;;ADzfpC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAA;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["React"]}