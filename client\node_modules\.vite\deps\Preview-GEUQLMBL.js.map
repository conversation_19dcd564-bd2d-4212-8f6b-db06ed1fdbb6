{"version": 3, "sources": ["../../react-player/dist/Preview.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nconst Preview = ({\n  src,\n  light,\n  oEmbedUrl,\n  onClickPreview,\n  playIcon,\n  previewTabIndex,\n  previewAriaLabel\n}) => {\n  const [image, setImage] = useState(null);\n  useEffect(() => {\n    if (!src || !light || !oEmbedUrl) return;\n    fetchImage({ src, light, oEmbedUrl });\n  }, [src, light, oEmbedUrl]);\n  const fetchImage = async ({\n    src: src2,\n    light: light2,\n    oEmbedUrl: oEmbedUrl2\n  }) => {\n    if (React.isValidElement(light2)) {\n      return;\n    }\n    if (typeof light2 === \"string\") {\n      setImage(light2);\n      return;\n    }\n    if (cache[src2]) {\n      setImage(cache[src2]);\n      return;\n    }\n    setImage(null);\n    const response = await fetch(oEmbedUrl2.replace(\"{url}\", src2));\n    const data = await response.json();\n    if (data.thumbnail_url) {\n      const fetchedImage = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n      setImage(fetchedImage);\n      cache[src2] = fetchedImage;\n    }\n  };\n  const handleKeyPress = (e) => {\n    if (e.key === \"Enter\" || e.key === \" \") {\n      onClickPreview == null ? void 0 : onClickPreview(e);\n    }\n  };\n  const handleClick = (e) => {\n    onClickPreview == null ? void 0 : onClickPreview(e);\n  };\n  const isElement = React.isValidElement(light);\n  const flexCenter = {\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  };\n  const styles = {\n    preview: {\n      width: \"100%\",\n      height: \"100%\",\n      backgroundImage: image && !isElement ? `url(${image})` : void 0,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      cursor: \"pointer\",\n      ...flexCenter\n    },\n    shadow: {\n      background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n      borderRadius: ICON_SIZE,\n      width: ICON_SIZE,\n      height: ICON_SIZE,\n      position: isElement ? \"absolute\" : void 0,\n      ...flexCenter\n    },\n    playIcon: {\n      borderStyle: \"solid\",\n      borderWidth: \"16px 0 16px 26px\",\n      borderColor: \"transparent transparent transparent white\",\n      marginLeft: \"7px\"\n    }\n  };\n  const defaultPlayIcon = /* @__PURE__ */ React.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ React.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n  return /* @__PURE__ */ React.createElement(\n    \"div\",\n    {\n      style: styles.preview,\n      className: \"react-player__preview\",\n      tabIndex: previewTabIndex,\n      onClick: handleClick,\n      onKeyPress: handleKeyPress,\n      ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n    },\n    isElement ? light : null,\n    playIcon || defaultPlayIcon\n  );\n};\nvar Preview_default = Preview;\nexport {\n  Preview_default as default\n};\n"], "mappings": ";;;;;;;;AAAA,mBAA2C;AAC3C,IAAM,YAAY;AAClB,IAAM,QAAQ,CAAC;AACf,IAAM,UAAU,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,IAAI;AACvC,8BAAU,MAAM;AACd,QAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAW;AAClC,eAAW,EAAE,KAAK,OAAO,UAAU,CAAC;AAAA,EACtC,GAAG,CAAC,KAAK,OAAO,SAAS,CAAC;AAC1B,QAAM,aAAa,OAAO;AAAA,IACxB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,WAAW;AAAA,EACb,MAAM;AACJ,QAAI,aAAAA,QAAM,eAAe,MAAM,GAAG;AAChC;AAAA,IACF;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS,MAAM;AACf;AAAA,IACF;AACA,QAAI,MAAM,IAAI,GAAG;AACf,eAAS,MAAM,IAAI,CAAC;AACpB;AAAA,IACF;AACA,aAAS,IAAI;AACb,UAAM,WAAW,MAAM,MAAM,WAAW,QAAQ,SAAS,IAAI,CAAC;AAC9D,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,QAAI,KAAK,eAAe;AACtB,YAAM,eAAe,KAAK,cAAc,QAAQ,cAAc,YAAY,EAAE,QAAQ,cAAc,QAAQ;AAC1G,eAAS,YAAY;AACrB,YAAM,IAAI,IAAI;AAAA,IAChB;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,MAAM;AAC5B,QAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,KAAK;AACtC,wBAAkB,OAAO,SAAS,eAAe,CAAC;AAAA,IACpD;AAAA,EACF;AACA,QAAM,cAAc,CAAC,MAAM;AACzB,sBAAkB,OAAO,SAAS,eAAe,CAAC;AAAA,EACpD;AACA,QAAM,YAAY,aAAAA,QAAM,eAAe,KAAK;AAC5C,QAAM,aAAa;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,EAClB;AACA,QAAM,SAAS;AAAA,IACb,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,iBAAiB,SAAS,CAAC,YAAY,OAAO,KAAK,MAAM;AAAA,MACzD,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU,YAAY,aAAa;AAAA,MACnC,GAAG;AAAA,IACL;AAAA,IACA,UAAU;AAAA,MACR,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,EACF;AACA,QAAM,kBAAkC,aAAAA,QAAM,cAAc,OAAO,EAAE,OAAO,OAAO,QAAQ,WAAW,uBAAuB,GAAmB,aAAAA,QAAM,cAAc,OAAO,EAAE,OAAO,OAAO,UAAU,WAAW,0BAA0B,CAAC,CAAC;AAC5O,SAAuB,aAAAA,QAAM;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,OAAO,OAAO;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,GAAG,mBAAmB,EAAE,cAAc,iBAAiB,IAAI,CAAC;AAAA,IAC9D;AAAA,IACA,YAAY,QAAQ;AAAA,IACpB,YAAY;AAAA,EACd;AACF;AACA,IAAI,kBAAkB;", "names": ["React"]}