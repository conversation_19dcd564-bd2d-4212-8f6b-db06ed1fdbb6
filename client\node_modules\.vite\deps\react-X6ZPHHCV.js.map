{"version": 3, "sources": ["../../tiktok-video-element/dist/react.js", "../../tiktok-video-element/dist/tiktok-video-element.js"], "sourcesContent": ["\"use client\";\n\n// dist/react.ts\nimport React from \"react\";\nimport CustomMediaElement from \"./tiktok-video-element.js\";\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n  \"style\",\n  \"children\",\n  \"ref\",\n  \"key\",\n  \"suppressContentEditableWarning\",\n  \"suppressHydrationWarning\",\n  \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n  className: \"class\",\n  htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n  return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n  if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n  if (typeof propValue === \"function\") return void 0;\n  if (typeof propValue === \"object\" && propValue !== null) return void 0;\n  return propValue;\n}\nfunction createComponent({\n  react: React2,\n  tagName,\n  elementClass,\n  events,\n  displayName,\n  defaultProps,\n  toAttributeName = defaultToAttributeName,\n  toAttributeValue = defaultToAttributeValue\n}) {\n  const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n  const ReactComponent = React2.forwardRef((props, ref) => {\n    var _a, _b;\n    const elementRef = React2.useRef(null);\n    const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n    const eventProps = {};\n    const attrs = {};\n    const reactProps = {};\n    const elementProps = {};\n    for (const [k, v] of Object.entries(props)) {\n      if (reservedReactProps.has(k)) {\n        reactProps[k] = v;\n        continue;\n      }\n      const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n      if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr) => attr === attrName))) {\n        elementProps[k] = v;\n        continue;\n      }\n      if (k.startsWith(\"on\")) {\n        eventProps[k] = v;\n        continue;\n      }\n      const attrValue = toAttributeValue(v);\n      if (attrName && attrValue != null) {\n        attrs[attrName] = String(attrValue);\n        if (!IS_REACT_19_OR_NEWER) {\n          reactProps[attrName] = attrValue;\n        }\n      }\n      if (attrName && IS_REACT_19_OR_NEWER) {\n        const attrValueFromDefault = defaultToAttributeValue(v);\n        if (attrValue !== attrValueFromDefault) {\n          reactProps[attrName] = attrValue;\n        } else {\n          reactProps[attrName] = v;\n        }\n      }\n    }\n    if (typeof window !== \"undefined\") {\n      for (const propName in eventProps) {\n        const callback = eventProps[propName];\n        const useCapture = propName.endsWith(\"Capture\");\n        const eventName = ((events == null ? void 0 : events[propName]) ?? propName.slice(2).toLowerCase()).slice(\n          0,\n          useCapture ? -7 : void 0\n        );\n        React2.useLayoutEffect(() => {\n          const eventTarget = elementRef == null ? void 0 : elementRef.current;\n          if (!eventTarget || typeof callback !== \"function\") return;\n          eventTarget.addEventListener(eventName, callback, useCapture);\n          return () => {\n            eventTarget.removeEventListener(eventName, callback, useCapture);\n          };\n        }, [elementRef == null ? void 0 : elementRef.current, callback]);\n      }\n      React2.useLayoutEffect(() => {\n        if (elementRef.current === null) return;\n        const newElemProps = /* @__PURE__ */ new Map();\n        for (const key in elementProps) {\n          setProperty(elementRef.current, key, elementProps[key]);\n          prevElemPropsRef.current.delete(key);\n          newElemProps.set(key, elementProps[key]);\n        }\n        for (const [key, _value] of prevElemPropsRef.current) {\n          setProperty(elementRef.current, key, void 0);\n        }\n        prevElemPropsRef.current = newElemProps;\n      });\n    }\n    if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n      const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n      const templateShadowRoot = React2.createElement(\"template\", {\n        shadowrootmode: mode,\n        shadowrootdelegatesfocus: delegatesFocus,\n        dangerouslySetInnerHTML: {\n          __html: elementClass.getTemplateHTML(attrs, props)\n        }\n      });\n      reactProps.children = [templateShadowRoot, reactProps.children];\n    }\n    return React2.createElement(tagName, {\n      ...defaultProps,\n      ...reactProps,\n      ref: React2.useCallback(\n        (node) => {\n          elementRef.current = node;\n          if (typeof ref === \"function\") {\n            ref(node);\n          } else if (ref !== null) {\n            ref.current = node;\n          }\n        },\n        [ref]\n      )\n    });\n  });\n  ReactComponent.displayName = displayName ?? elementClass.name;\n  return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n  var _a;\n  node[name] = value;\n  if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n    node.removeAttribute(name);\n  }\n}\n\n// dist/react.ts\nvar react_default = createComponent({\n  react: React,\n  tagName: \"tiktok-video\",\n  elementClass: CustomMediaElement,\n  toAttributeName(propName) {\n    if (propName === \"muted\") return \"\";\n    if (propName === \"defaultMuted\") return \"muted\";\n    return defaultToAttributeName(propName);\n  }\n});\nexport {\n  react_default as default\n};\n/*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/\n", "const EMBED_BASE = \"https://www.tiktok.com/player/v1\";\nconst MATCH_SRC = /tiktok\\.com\\/(?:@[^/]+\\/video\\/)?(\\d+)(?:\\/([\\w-]+))?/;\nconst PlayerState = { INIT: -1, ENDED: 0, PLAYING: 1, PAUSED: 2, BUFFERING: 3 };\nconst EventMap = {\n  [PlayerState.INIT]: \"emptied\",\n  [PlayerState.PAUSED]: \"pause\",\n  [PlayerState.ENDED]: \"ended\",\n  [PlayerState.PLAYING]: \"play\",\n  [PlayerState.BUFFERING]: \"waiting\"\n};\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; autoplay; fullscreen; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display:inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position:absolute;\n        top:0;\n        left:0;\n        width:100%;\n        height:100%;\n        border:0;\n      }\n    </style>\n    <iframe ${serializeAttributes(iframeAttrs)} title=\"TikTok video\"></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props = {}) {\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const srcId = matches && matches[1];\n  const params = {\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    muted: attrs.muted,\n    loop: attrs.loop,\n    rel: 0,\n    ...props.config\n  };\n  return `${EMBED_BASE}/${srcId}?${serialize(params)}`;\n}\nclass TikTokVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static get observedAttributes() {\n    return [\"src\", \"controls\", \"loop\", \"autoplay\", \"muted\"];\n  }\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #muted = false;\n  #currentTime = 0;\n  #paused = true;\n  #config = null;\n  #volume = 100;\n  #duration = 0;\n  #iframe;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow(TikTokVideoElement.shadowRootOptions);\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#currentTime = 0;\n    this.#muted = false;\n    this.#paused = true;\n    if (!this.src) {\n      this.shadowRoot.innerHTML = \"\";\n      globalThis.removeEventListener(\"message\", this.#onMessage);\n      return;\n    }\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    const attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.#iframe = iframe;\n    globalThis.addEventListener(\"message\", this.#onMessage);\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"muted\": {\n        await this.loadComplete;\n        this.muted = newValue != null;\n        break;\n      }\n      case \"autoplay\":\n      case \"controls\":\n      case \"loop\":\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  #onMessage = (event) => {\n    const msg = event.data;\n    if (!(msg == null ? void 0 : msg[\"x-tiktok-player\"])) return;\n    switch (msg.type) {\n      case \"onPlayerReady\":\n        this.loadComplete.resolve();\n        break;\n      case \"onStateChange\": {\n        this.#paused = [PlayerState.INIT, PlayerState.PAUSED, PlayerState.ENDED].includes(msg.value);\n        const eventType = EventMap[msg.value];\n        if (eventType) this.dispatchEvent(new Event(eventType));\n        break;\n      }\n      case \"onCurrentTime\":\n        this.#currentTime = msg.value.currentTime;\n        this.#duration = msg.value.duration;\n        this.dispatchEvent(new Event(\"durationchange\"));\n        this.dispatchEvent(new Event(\"timeupdate\"));\n        break;\n      case \"onVolumeChange\":\n        this.#volume = msg.value;\n        this.dispatchEvent(new Event(\"volumechange\"));\n        break;\n      case \"onMute\":\n        this.#muted = msg.value ? true : false;\n        this.#volume = msg.value ? 0 : this.#volume;\n        this.dispatchEvent(new Event(\"volumechange\"));\n        break;\n      case \"onError\":\n        this.dispatchEvent(new Event(\"error\"));\n        break;\n      default:\n        console.warn(\"Unhandled TikTok player message:\", msg);\n        break;\n    }\n  };\n  #post(type, value) {\n    var _a;\n    if (!((_a = this.#iframe) == null ? void 0 : _a.contentWindow)) return;\n    const message = { \"x-tiktok-player\": true, type, ...value !== void 0 ? { value } : {} };\n    this.#iframe.contentWindow.postMessage(message, \"*\");\n  }\n  async play() {\n    await this.loadComplete;\n    this.#post(\"play\");\n  }\n  async pause() {\n    await this.loadComplete;\n    this.#post(\"pause\");\n  }\n  async #seekTo(sec) {\n    await this.loadComplete;\n    this.#post(\"seekTo\", Number(sec));\n  }\n  async #mute() {\n    await this.loadComplete;\n    this.#post(\"mute\");\n  }\n  async #unMute() {\n    await this.loadComplete;\n    this.#post(\"unMute\");\n  }\n  get volume() {\n    return this.#volume / 100;\n  }\n  set volume(_val) {\n    console.warn(\"Volume control is not supported for TikTok videos.\");\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    this.#seekTo(val);\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    this.#muted = val;\n    val ? this.#mute() : this.#unMute();\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    this.toggleAttribute(\"muted\", !!val);\n  }\n  get paused() {\n    return this.#paused;\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    this.setAttribute(\"src\", val ?? \"\");\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"tiktok-video\")) {\n  globalThis.customElements.define(\"tiktok-video\", TikTokVideoElement);\n}\nvar tiktok_video_element_default = TikTokVideoElement;\nexport {\n  tiktok_video_element_default as default\n};\n"], "mappings": ";;;;;;;;;AAGA,mBAAkB;;;ACHlB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,cAAc,EAAE,MAAM,IAAI,OAAO,GAAG,SAAS,GAAG,QAAQ,GAAG,WAAW,EAAE;AAC9E,IAAM,WAAW;AAAA,EACf,CAAC,YAAY,IAAI,GAAG;AAAA,EACpB,CAAC,YAAY,MAAM,GAAG;AAAA,EACtB,CAAC,YAAY,KAAK,GAAG;AAAA,EACrB,CAAC,YAAY,OAAO,GAAG;AAAA,EACvB,CAAC,YAAY,SAAS,GAAG;AAC3B;AACA,SAAS,gBAAgB,OAAO,QAAQ,CAAC,GAAG;AAC1C,QAAM,cAAc;AAAA,IAClB,KAAK,mBAAmB,OAAO,KAAK;AAAA,IACpC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ;AAChB,gBAAY,aAAa,IAAI,KAAK,UAAU,MAAM,MAAM;AAAA,EAC1D;AACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAiBU,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAG9C;AACA,SAAS,mBAAmB,OAAO,QAAQ,CAAC,GAAG;AAC7C,MAAI,CAAC,MAAM,IAAK;AAChB,QAAM,UAAU,MAAM,IAAI,MAAM,SAAS;AACzC,QAAM,QAAQ,WAAW,QAAQ,CAAC;AAClC,QAAM,SAAS;AAAA,IACb,UAAU,MAAM,aAAa,KAAK,OAAO;AAAA,IACzC,UAAU,MAAM;AAAA,IAChB,OAAO,MAAM;AAAA,IACb,MAAM,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,GAAG,MAAM;AAAA,EACX;AACA,SAAO,GAAG,UAAU,IAAI,KAAK,IAAI,UAAU,MAAM,CAAC;AACpD;AACA,IAAM,qBAAN,MAAM,6BAA4B,WAAW,eAAe,MAAM;AAClE,GAAG;AAAA,EACD,OAAO,kBAAkB;AAAA,EACzB,OAAO,oBAAoB,EAAE,MAAM,OAAO;AAAA,EAC1C,WAAW,qBAAqB;AAC9B,WAAO,CAAC,OAAO,YAAY,QAAQ,YAAY,OAAO;AAAA,EACxD;AAAA,EACA,eAAe,IAAI,cAAc;AAAA,EACjC;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,eAAe;AAAA,EACf,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,eAAgB;AACzB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,oBAAmB,iBAAiB;AAAA,IACxD;AACA,UAAM,cAAc,CAAC,KAAK;AAC1B,QAAI,KAAK,YAAY;AACnB,WAAK,eAAe,IAAI,cAAc;AAAA,IACxC;AACA,SAAK,aAAa;AAClB,WAAO,KAAK,iBAAiB,QAAQ,QAAQ;AAC7C,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,QAAI,CAAC,KAAK,KAAK;AACb,WAAK,WAAW,YAAY;AAC5B,iBAAW,oBAAoB,WAAW,KAAK,UAAU;AACzD;AAAA,IACF;AACA,QAAI,SAAS,KAAK,WAAW,cAAc,QAAQ;AACnD,UAAM,QAAQ,qBAAqB,KAAK,UAAU;AAClD,QAAI,eAAe,QAAQ;AACzB,WAAK,UAAU,KAAK,MAAM,OAAO,aAAa,aAAa,KAAK,IAAI;AAAA,IACtE;AACA,QAAI,EAAE,UAAU,OAAO,SAAS,OAAO,QAAQ,OAAO,QAAQ,mBAAmB,OAAO,IAAI,GAAG;AAC7F,WAAK,WAAW,YAAY,gBAAgB,OAAO,IAAI;AACvD,eAAS,KAAK,WAAW,cAAc,QAAQ;AAAA,IACjD;AACA,SAAK,UAAU;AACf,eAAW,iBAAiB,WAAW,KAAK,UAAU;AAAA,EACxD;AAAA,EACA,MAAM,yBAAyB,UAAU,UAAU,UAAU;AAC3D,QAAI,aAAa,SAAU;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK,SAAS;AACZ,cAAM,KAAK;AACX,aAAK,QAAQ,YAAY;AACzB;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACV,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,CAAC,UAAU;AACtB,UAAM,MAAM,MAAM;AAClB,QAAI,EAAE,OAAO,OAAO,SAAS,IAAI,iBAAiB,GAAI;AACtD,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK;AACH,aAAK,aAAa,QAAQ;AAC1B;AAAA,MACF,KAAK,iBAAiB;AACpB,aAAK,UAAU,CAAC,YAAY,MAAM,YAAY,QAAQ,YAAY,KAAK,EAAE,SAAS,IAAI,KAAK;AAC3F,cAAM,YAAY,SAAS,IAAI,KAAK;AACpC,YAAI,UAAW,MAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACtD;AAAA,MACF;AAAA,MACA,KAAK;AACH,aAAK,eAAe,IAAI,MAAM;AAC9B,aAAK,YAAY,IAAI,MAAM;AAC3B,aAAK,cAAc,IAAI,MAAM,gBAAgB,CAAC;AAC9C,aAAK,cAAc,IAAI,MAAM,YAAY,CAAC;AAC1C;AAAA,MACF,KAAK;AACH,aAAK,UAAU,IAAI;AACnB,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C;AAAA,MACF,KAAK;AACH,aAAK,SAAS,IAAI,QAAQ,OAAO;AACjC,aAAK,UAAU,IAAI,QAAQ,IAAI,KAAK;AACpC,aAAK,cAAc,IAAI,MAAM,cAAc,CAAC;AAC5C;AAAA,MACF,KAAK;AACH,aAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AACrC;AAAA,MACF;AACE,gBAAQ,KAAK,oCAAoC,GAAG;AACpD;AAAA,IACJ;AAAA,EACF;AAAA,EACA,MAAM,MAAM,OAAO;AACjB,QAAI;AACJ,QAAI,GAAG,KAAK,KAAK,YAAY,OAAO,SAAS,GAAG,eAAgB;AAChE,UAAM,UAAU,EAAE,mBAAmB,MAAM,MAAM,GAAG,UAAU,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE;AACtF,SAAK,QAAQ,cAAc,YAAY,SAAS,GAAG;AAAA,EACrD;AAAA,EACA,MAAM,OAAO;AACX,UAAM,KAAK;AACX,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,KAAK;AACX,SAAK,MAAM,OAAO;AAAA,EACpB;AAAA,EACA,MAAM,QAAQ,KAAK;AACjB,UAAM,KAAK;AACX,SAAK,MAAM,UAAU,OAAO,GAAG,CAAC;AAAA,EAClC;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,KAAK;AACX,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,MAAM,UAAU;AACd,UAAM,KAAK;AACX,SAAK,MAAM,QAAQ;AAAA,EACrB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO,MAAM;AACf,YAAQ,KAAK,oDAAoD;AAAA,EACnE;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,QAAQ,GAAG;AAAA,EAClB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AACd,UAAM,KAAK,MAAM,IAAI,KAAK,QAAQ;AAAA,EACpC;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,SAAK,gBAAgB,SAAS,CAAC,CAAC,GAAG;AAAA,EACrC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,aAAa,OAAO,OAAO,EAAE;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAM,QAAQ,KAAK,IAAI;AACvB,aAAO,KAAK,IAAI;AAChB,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,WAAW,MAAM;AAAA,EAC7B,GAAG;AACD,QAAI,KAAK;AACT,UAAM,CAAC,SAAS,WAAW;AACzB,eAAS,SAAS,MAAM;AACxB,YAAM;AACN,YAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,cAAc;AAC7B,QAAI,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,QAAQ,QAAQ,QAAQ,GAAI,GAAE,GAAG,IAAI;AAAA,aAChC,QAAQ,MAAO,GAAE,GAAG,IAAI;AAAA,aACxB,OAAO,KAAM,GAAE,GAAG,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,IAAI,gBAAgB,aAAa,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,OAAO;AACX,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,GAAI,SAAQ,IAAI,WAAW,GAAG,CAAC;AAAA,QACxC,SAAQ,IAAI,WAAW,GAAG,CAAC,KAAK,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ;AACtJ;AACA,IAAI,WAAW,kBAAkB,CAAC,WAAW,eAAe,IAAI,cAAc,GAAG;AAC/E,aAAW,eAAe,OAAO,gBAAgB,kBAAkB;AACrE;AACA,IAAI,+BAA+B;;;AD7RnC,IAAI,qBAAqC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,yBAAyB;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AACX;AACA,SAAS,uBAAuB,UAAU;AACxC,SAAO,SAAS,YAAY;AAC9B;AACA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,OAAO,cAAc,UAAW,QAAO,YAAY,KAAK;AAC5D,MAAI,OAAO,cAAc,WAAY,QAAO;AAC5C,MAAI,OAAO,cAAc,YAAY,cAAc,KAAM,QAAO;AAChE,SAAO;AACT;AACA,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AACrB,GAAG;AACD,QAAM,uBAAuB,OAAO,SAAS,OAAO,OAAO,KAAK;AAChE,QAAM,iBAAiB,OAAO,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAI,IAAI;AACR,UAAM,aAAa,OAAO,OAAO,IAAI;AACrC,UAAM,mBAAmB,OAAO,OAAuB,oBAAI,IAAI,CAAC;AAChE,UAAM,aAAa,CAAC;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAI,mBAAmB,IAAI,CAAC,GAAG;AAC7B,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,WAAW,gBAAgB,uBAAuB,CAAC,KAAK,CAAC;AAC/D,UAAI,KAAK,aAAa,aAAa,EAAE,QAAQ,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,aAAa,uBAAuB,OAAO,SAAS,GAAG,KAAK,CAAC,SAAS,SAAS,QAAQ,IAAI;AACvN,qBAAa,CAAC,IAAI;AAClB;AAAA,MACF;AACA,UAAI,EAAE,WAAW,IAAI,GAAG;AACtB,mBAAW,CAAC,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,iBAAiB,CAAC;AACpC,UAAI,YAAY,aAAa,MAAM;AACjC,cAAM,QAAQ,IAAI,OAAO,SAAS;AAClC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY,sBAAsB;AACpC,cAAM,uBAAuB,wBAAwB,CAAC;AACtD,YAAI,cAAc,sBAAsB;AACtC,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,aAAa;AACjC,iBAAW,YAAY,YAAY;AACjC,cAAM,WAAW,WAAW,QAAQ;AACpC,cAAM,aAAa,SAAS,SAAS,SAAS;AAC9C,cAAM,cAAc,UAAU,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,MAAM,CAAC,EAAE,YAAY,GAAG;AAAA,UAClG;AAAA,UACA,aAAa,KAAK;AAAA,QACpB;AACA,eAAO,gBAAgB,MAAM;AAC3B,gBAAM,cAAc,cAAc,OAAO,SAAS,WAAW;AAC7D,cAAI,CAAC,eAAe,OAAO,aAAa,WAAY;AACpD,sBAAY,iBAAiB,WAAW,UAAU,UAAU;AAC5D,iBAAO,MAAM;AACX,wBAAY,oBAAoB,WAAW,UAAU,UAAU;AAAA,UACjE;AAAA,QACF,GAAG,CAAC,cAAc,OAAO,SAAS,WAAW,SAAS,QAAQ,CAAC;AAAA,MACjE;AACA,aAAO,gBAAgB,MAAM;AAC3B,YAAI,WAAW,YAAY,KAAM;AACjC,cAAM,eAA+B,oBAAI,IAAI;AAC7C,mBAAW,OAAO,cAAc;AAC9B,sBAAY,WAAW,SAAS,KAAK,aAAa,GAAG,CAAC;AACtD,2BAAiB,QAAQ,OAAO,GAAG;AACnC,uBAAa,IAAI,KAAK,aAAa,GAAG,CAAC;AAAA,QACzC;AACA,mBAAW,CAAC,KAAK,MAAM,KAAK,iBAAiB,SAAS;AACpD,sBAAY,WAAW,SAAS,KAAK,MAAM;AAAA,QAC7C;AACA,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,gBAAgB,gBAAgB,OAAO,SAAS,aAAa,qBAAqB,gBAAgB,OAAO,SAAS,aAAa,oBAAoB;AACvK,YAAM,EAAE,MAAM,eAAe,IAAI,aAAa;AAC9C,YAAM,qBAAqB,OAAO,cAAc,YAAY;AAAA,QAC1D,gBAAgB;AAAA,QAChB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACnD;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,CAAC,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AACA,WAAO,OAAO,cAAc,SAAS;AAAA,MACnC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK,OAAO;AAAA,QACV,CAAC,SAAS;AACR,qBAAW,UAAU;AACrB,cAAI,OAAO,QAAQ,YAAY;AAC7B,gBAAI,IAAI;AAAA,UACV,WAAW,QAAQ,MAAM;AACvB,gBAAI,UAAU;AAAA,UAChB;AAAA,QACF;AAAA,QACA,CAAC,GAAG;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,iBAAe,cAAc,eAAe,aAAa;AACzD,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM,OAAO;AACtC,MAAI;AACJ,OAAK,IAAI,IAAI;AACb,MAAI,SAAS,QAAQ,WAAW,KAAK,WAAW,gBAAgB,OAAO,SAAS,GAAG,cAAc,CAAC,IAAI;AACpG,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAGA,IAAI,gBAAgB,gBAAgB;AAAA,EAClC,OAAO,aAAAA;AAAA,EACP,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAS,QAAO;AACjC,QAAI,aAAa,eAAgB,QAAO;AACxC,WAAO,uBAAuB,QAAQ;AAAA,EACxC;AACF,CAAC;", "names": ["React"]}