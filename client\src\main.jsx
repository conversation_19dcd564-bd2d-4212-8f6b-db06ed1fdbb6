
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import{<PERSON><PERSON><PERSON><PERSON>out<PERSON>} from 'react-router-dom'
import { ClerkProvider } from '@clerk/clerk-react'


const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error('Missing Publishable Key')
}

createRoot(document.getElementById('root')).render(

  <ClerkProvider publishableKey={PUBLISHABLE_KEY}>
   <BrowserRouter>
   <App />
 </BrowserRouter>
 </ClerkProvider>,
)
