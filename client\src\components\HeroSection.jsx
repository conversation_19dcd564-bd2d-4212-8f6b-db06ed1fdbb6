import React from 'react'
import { assets } from '../assets/assets'
import {  ArrowR<PERSON>, CalendarIcon, ClockIcon } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

const HeroSection = () => {

    const navigate =useNavigate()


  return (
    <div className='flex flex-col items-start justify-center gap-8 px-6 md:px-16 lg:px-36 bg-[url("/backgroundImage.png")] bg-cover bg-center h-screen'>
        <img src={assets.marvelLogo} alt="" className='max-h-11 lg:max-h-11 mt-20'/>

        <h1 className='text=5xl md:text-[70px] md:leading-18 font-semibold max-w-110'>
            Guardians <br /> of the Galaxy
        </h1>
      <div className='flex items-center gap-4 text-gray-300'>
        <span>Action | Adventure | Sci-Fi</span>

        <div className='flex items-center gap-1'>
         <CalendarIcon className='w-4.5 h-4.5' />2018
        </div>
        <div className='flex items-center gap-1'>
            <ClockIcon  className='w-4.5 h-4.5'/>2h.18
        </div>

      </div>

        <p className='max-w-md text-gray-300'>In a post-apocalyptic future, a group of space travelers battle
        their way through various challenges,
        dangerous criminals, and their own personal demons, in order to return
        home to Earth.</p>
        <button onClick={() => navigate('/movies')} className='flex items-center gap-2 px-4 py-1 sm:px-7 sm:py-2 bg-primary
         hover:bg-primary-dull transition rounded-full font-medium cursor-pointer'>Explore Movies
        
        <ArrowRight  className='w-5 h-5'/>

        </button>

    </div>
  )
}

export default HeroSection
